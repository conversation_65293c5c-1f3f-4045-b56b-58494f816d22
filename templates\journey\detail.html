{% extends "base.html" %}

{% block title %}
{{ journey.title }} - Footprints
{% endblock %}

{% block head %}
<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/journey-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/event-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/location-operations.js') }}"></script>
{% endblock %}

{% block content %}
<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back</span>
</a>
<div class="row g-4" id="journeyContainer">
  <!-- Journey details panel -->
  <div class="col-md-4">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h1 class="fs-3 fw-bold mb-0">Journey Details</h1>

          <!-- Journey menu -->
          {% if session.get('user_id') %}
          <div class="dropdown">
            <button class="btn btn-dark btn-sm rounded-pill d-flex align-items-center px-3 journey-menu-btn"
              id="journey-menu" type="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-three-dots-vertical me-1"></i>
              <span class="d-none d-sm-inline">Actions</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow-sm">
              {% if journey.user_id != session.user_id %}

              <li>
                <button type="button" class="dropdown-item"
                        data-action="toggle-follow"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-heart{% if is_following_journey %}-fill{% endif %} me-2"></i>
                  <span class="btn-text">{% if is_following_journey %}Unfollow{% else %}Follow{% endif %} Journey</span>
                </button>
              </li>

              {% endif %}
              {% if journey.user_id == session.user_id or session.get('role') in ['editor', 'admin', 'support_tech'] %}
              <li>
                {% if journey.no_edits and journey.user_id != session.user_id %}
                <a href="#" class="dropdown-item text-muted" onclick="showProtectedJourneyMessage()"
                  style="cursor: not-allowed;">
                  <i class="bi bi-shield-lock me-2"></i>Edit Journey (Protected)
                </a>
                {% else %}
                <button type="button" class="dropdown-item"
                        data-action="edit-journey"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-pencil me-2"></i>Edit Journey
                </button>
                {% endif %}
              </li>
              {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
              <li>
                <a href="{{ url_for('edit_history.get_journey_edit_history', journey_id=journey.id) }}"
                  class="dropdown-item">
                  <i class="bi bi-clock-history me-2"></i>View Edit History
                </a>
              </li>
              {% endif %}
              {% if session.get('role') in ['editor', 'admin', 'support_tech'] and not journey.user_id ==
              session.user_id %}
              <li>
                <button type="button" class="dropdown-item text-warning"
                        data-action="toggle-hidden"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-slash-circle me-2"></i>
                  <span class="btn-text">{% if not journey.is_hidden %}Hide{% else %}Unhide{% endif %} Journey</span>
                </button>
              </li>
              {% endif %}
              {% if journey.user_id == session.user_id %}
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <form method="post"
                  action="{{ url_for('journey.delete_' + ('admin_' if is_admin_view else '') + 'journey', journey_id=journey.id) }}"
                  id="deleteJourneyForm" class="d-inline">
                  <button type="button" class="dropdown-item text-danger"
                          data-action="delete-journey"
                          data-journey-id="{{ journey.id }}">
                    <i class="bi bi-trash me-2"></i>Delete Journey
                  </button>
                </form>
              </li>
              {% endif %}
              {% endif %}
            </ul>
          </div>
          {% else %}
          {% endif %}
        </div>
        <div class="mb-2">
          <span
            class="badge {% if journey.visibility in ('public', 'published') %}bg-success{% else %}bg-secondary{% endif %} rounded-pill py-1 px-3">
            {% if journey.visibility in ('public', 'published') %}{{ journey.visibility|capitalize }}{% else
            %}Private{% endif %}
          </span>
          {% if journey.is_hidden %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1">Hidden</span>
          {% endif %}
          {% if journey.no_edits and (journey.user_id == session.user_id or can_manage_content()) %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1"
            title="This journey is protected from staff edits">
            <i class="bi bi-shield-lock me-1"></i>Protected
          </span>
          {% endif %}
        </div>

        <!-- Hidden Journey Appeal Section -->
        {% if journey.is_hidden and journey.user_id == session.user_id %}
        <div class="alert alert-warning rounded-3 mb-2" id="appealSection">
          <div class="d-flex align-items-start">
            <i class="bi bi-eye-slash fs-5 me-3 mt-1"></i>
            <div class="flex-grow-1">
              <h6 class="fw-bold mb-2">Journey Hidden by Staff</h6>
              <p class="mb-2 small">This journey has been hidden by content management staff and is not visible to other
                users.</p>

              <!-- Show rejection reason if appeal was rejected -->
              {% if appeal_status and appeal_status.status == 'rejected' and appeal_status.admin_response %}
              <div class="bg-light rounded p-3 mb-3" id="rejectionReason">
                <h6 class="small fw-bold mb-1 text-danger">
                  <i class="bi bi-x-circle me-1"></i>Appeal Rejected - Staff Response:
                </h6>
                <p class="mb-0 small">{{ appeal_status.admin_response }}</p>
              </div>
              {% endif %}

              <a href="{{ url_for('helpdesk.journey_appeal', journey_id=journey.id) }}"
                class="btn btn-sm btn-warning rounded-pill">
                <i class="bi bi-flag me-1"></i>
                {% if not appeal_status %}
                Submit Appeal
                {% elif appeal_status.status == 'rejected' %}
                Submit New Appeal
                {% elif appeal_status.status in ['new', 'open'] %}
                View Appeal Status
                {% else %}
                Appeal Decision
                {% endif %}
              </a>
            </div>
          </div>
        </div>
        {% endif %}

        <!-- Author info -->
        <div class="d-flex align-items-center mb-4">
          <div class="position-relative me-3">
            <div class="rounded-circle overflow-hidden">
              <img {% if journey.profile_image %}
                src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;"
                onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
              {% else %}
              <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;">
              {% endif %}
            </div>
          </div>
        
          <div class="d-flex flex-column align-items-start">
            {% if journey.user_id != session.user_id %}
            <a href="{{ url_for('account.get_public_profile', username=journey.username) }}" 
               class="text-decoration-none username-link text-primary fw-semibold ">
              {{ journey.username }}
            </a>
            {% else %}
            <div class="fw-semibold">{{ journey.username }}</div>
            {% endif %}
            <small class="text-muted">Created on {{ journey.start_date.strftime('%B %d, %Y') }}</small>
          </div>
        </div>
        

        <!-- Journey info -->
        <div class="journey-info">
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Title</h6>
            <p class="text-dark fs-4 fw-medium mb-0" id="journeyTitle">{{ journey.title }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Start Date</h6>
            <p class="mb-0" id="journeyStartDate">{{ journey.start_date.strftime('%B %d, %Y') }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
            <p class="mb-0" id="journeyDescription">{{ journey.description }}</p>
          </div>
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Cover Image</h6>
            <div class="cover-image-container mb-2">
              {% if journey.cover_image %}
              <div class="cover-image position-relative" id="coverImageContainer">
                <div class="cover-image-clickable"
                  onclick="showCoverImageModal('{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}', '{{ journey.title|replace("'", "\\'") }}')">
                  <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                    alt="Cover image" class="cover-image-img" id="coverImageImg">
                  <div class="cover-image-overlay">
                    <i class="bi bi-zoom-in"></i>
                    <span>Click to enlarge</span>
                  </div>
                </div>
                {% if journey.user_id == session.user_id or session.get('role') in ['editor', 'admin', 'support_tech']
                %}
                <div class="cover-image-controls position-absolute bottom-0 end-0 m-2">
                  {% if journey.user_id == session.user_id %}
                  <button class="btn btn-sm btn-light rounded-pill me-2" id="changeCoverImageBtn">
                    <i class="bi bi-image me-1"></i> Change
                  </button>
                  {% endif %}
                  <button class="btn btn-sm btn-light rounded-pill" id="removeCoverImageBtn">
                    <i class="bi bi-trash me-1"></i> Remove
                  </button>
                </div>
                {% endif %}
              </div>
              {% else %}
              <div class="cover-image cover-image-placeholder d-flex align-items-center justify-content-center"
                id="coverImagePlaceholder">
                <div class="text-center">
                  {% if journey.user_id == session.user_id %}
                  {% if premium_access %}
                  <button class="btn btn-light btn-sm rounded-pill" id="addCoverImageBtn">
                    <i class="bi bi-image me-2"></i> Add Cover Image
                  </button>
                  {% else %}
                  <div class="text-muted small mb-2">Cover images are available for premium users</div>
                  <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
                    class="btn btn-primary btn-sm rounded-pill">
                    <i class="bi bi-star me-1"></i> Upgrade to Premium
                  </a>
                  {% endif %}
                  {% else %}
                  <div class="text-muted">No cover image</div>
                  {% endif %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Journey Map -->
          {% if locations and locations|length > 0 %}
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="text-uppercase text-muted small fw-bold mb-0">Journey Map</h6>
              <a href="{{url_for('location.get_journey_map', journey_id=journey.id)}}"
                class="btn btn-sm btn-outline-primary" target="_blank">
                <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
              </a>
            </div>
            <div class="journey-map-container">
              <div id="journeyMap" style="height: 250px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
            </div>
          </div>
          {% endif %}
        </div>



      </div>
    </div>
  </div>

  <!-- Events panel -->
  <div class="col-md-8">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fs-3 fw-bold mb-0">Events <span class="badge bg-dark rounded-pill" id="eventCount">{{
              events|length }}</span></h2>
          {% if session.get('user_id') and journey.user_id == session.user_id and events %}
          <button class="btn btn-dark rounded-pill px-4 py-2"
                  data-action="create-event"
                  data-journey-id="{{ journey.id }}">
            <i class="bi bi-plus-lg me-2"></i> Add event
          </button>
          {% endif %}
        </div>

        {% if events %}
        <div class="event-content">
          <div class="timeline-container overflow-auto">
            <div class="timeline">
              {% set ns = namespace(displayed_dates={}) %}
              {% for event in events %}
              {% set current_date = event.start_datetime.strftime('%Y-%m-%d') %}

              <div class="timeline-item">
                {% if current_date not in ns.displayed_dates %}
                <div class="timeline-date-marker">
                  <div class="timeline-date">
                    <span class="badge bg-dark rounded-pill py-1 px-3">{{ event.start_datetime.strftime('%b %d, %Y')
                      }}</span>
                  </div>
                </div>
                {% set _ = ns.displayed_dates.update({current_date: true}) %}
                {% endif %}

                <div class="timeline-content-wrapper event-card" data-event-id="{{ event.id }}">
                  <div class="timeline-content card border-0 shadow-sm">
                    <div class="card-body p-0">
                      <!-- Main event content -->
                      <div class="d-flex event-main-content"
                        onclick="window.location.href='{{url_for('event.get_event_details', event_id=event.id)}}'"
                        style="cursor: pointer;">
                        {% if event.image %}
                        <div class="timeline-image-container">
                          <img src="{{ url_for('static', filename=get_safe_image_url(event.image, 'event')) }}"
                            class="timeline-image rounded-start" alt="{{ event.title }}">
                        </div>
                        {% else %}
                        <div class="timeline-image-container">
                          <img
                            src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                            class="timeline-image rounded-start" alt="Event placeholder" />
                        </div>
                        {% endif %}

                        <div class="p-3 w-100 position-relative">
                          <h5 class="mb-2 fw-semibold text-truncate">{{ event.title }}</h5>
                          <p class="mb-2 text-muted small"
                            style="min-height: 40px; max-height: 60px; overflow: hidden;">{{ event.description }}
                          </p>

                          <div class="d-flex justify-content-between align-items-center">
                            {% if session.get('user_id') %}
                            <div class="d-flex align-items-center text-primary small event-location-button"
                              style="cursor: pointer;" data-event-id="{{ event.id }}"
                              data-latitude="{{ event.latitude }}" data-longitude="{{ event.longitude }}"
                              onclick="event.stopPropagation(); toggleEventMap({{ event.id }}, {{ event.latitude or 'null' }}, {{ event.longitude or 'null' }}, '{{ event.location_name|replace("'", "\\'") }}')">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                              <i class="bi bi-chevron-down ms-2 map-toggle-icon" id="mapToggle{{ event.id }}"></i>
                            </div>
                            {% else %}
                            <!-- Non-logged-in users see location but cannot interact -->
                            <div class="d-flex align-items-center text-muted small">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                            </div>
                            {% endif %}
                            <small class="text-muted">{{ event.start_datetime.strftime('%I:%M %p') }}</small>
                          </div>
                        </div>
                      </div>

                      <!-- Collapsible map section -->
                      <div class="event-map-container" id="eventMap{{ event.id }}" style="display: none;">
                        <div class="border-top">
                          <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                              <h6 class="mb-0 text-muted">Location</h6>
                              {% if session.get('user_id') %}
                              <button class="btn btn-sm btn-outline-primary"
                                onclick="openFullMap({{ event.latitude or 'null' }}, {{ event.longitude or 'null' }}, '{{ event.location_name|replace("'", "\\'") }}')">
                                <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
                              </button>
                              {% endif %}
                            </div>
                            <div id="inlineMap{{ event.id }}"
                              style="height: 200px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {% endfor %}
            </div>
          </div>
        </div>
        {% else %}
        <div class="event-content d-flex flex-column justify-content-center align-items-center">
          <div class="empty-state text-center">
            <div class="empty-state-icon mb-4">
              <i class="bi bi-calendar-x" style="font-size: 5rem; color: #d1d1d1;"></i>
            </div>
            <h3 class="mb-3 fw-bold" id="noEventsMessage">No events yet</h3>
            <p class="text-muted mb-4">{% if journey.user_id == session.user_id %}Start your journey by adding your
              first
              event!{% else %}This journey doesn't have any events yet.{% endif %}</p>
            {% if session.get('user_id') and journey.user_id == session.user_id %}
            <button class="btn btn-primary rounded-pill px-4 py-2"
                    data-action="create-event"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-plus-lg me-2"></i> Add your first event
            </button>
            {% endif %}
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Hidden file input for cover image uploads -->
<input type="file" id="coverImageInput" accept=".png,.jpg,.jpeg,.gif" style="display: none;">

<style>
.username-link {
  transition: all 0.2s ease;
  padding: 2px 6px;
  border-radius: 4px;
  margin: -2px -6px; 
  display: inline; 
}

.username-link:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}



  .rounded-circle,
  .rounded-circle img {
    position: relative;
    z-index: 1;
  }

  .map-preview-container {
    position: relative;
    display: inline-block;
    z-index: 2;
  }


  .view-map-link {
    text-decoration: none;
    color: black;
  }

  .view-map-link:hover {
    text-decoration: none;
    color: grey;
  }


  .map-preview {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    height: 200px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 1080;
    overflow: hidden;
    text-align: center;
  }

  .map-preview.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  .map-preview::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #c8e6c9 0%, #fff59d 50%);
    border-left: 2px solid #e0e0e0;
    border-top: 2px solid #e0e0e0;
    transform: rotate(45deg);
    z-index: 1080;
  }

  /* Journey Map Styling */
  .journey-map-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #journeyMap {
    border-radius: 8px;
  }

  .leaflet-popup-content {
    margin: 8px 12px;
  }

  .event-popup h6 {
    color: #495057;
    margin-bottom: 8px;
  }

  .location-popup h6 {
    color: #495057;
    margin-bottom: 8px;
  }

  .journey-menu-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .journey-menu-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
  }

  .journey-menu-btn:active {
    transform: translateY(0);
  }

  /* Event map styling */
  .event-map-container {
    transition: all 0.3s ease;
  }

  .event-location-button {
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 6px;
  }

  .event-location-button:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
  }

  .map-toggle-icon {
    transition: transform 0.2s ease;
  }

  .event-main-content {
    transition: background-color 0.2s ease;
  }

  .event-main-content:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .event-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .cover-image-img {
    width: 80%;
    height: 100px;
    border-radius: 8px;
    display: block;
  }

  #journeyContainer {
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: row;
  }

  #journeyContainer .col-md-4,
  #journeyContainer .col-md-8 {
    display: flex;
    flex-direction: column;
  }

  #journeyContainer .card {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  #journeyContainer .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .event-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .event-content.d-flex.flex-column.justify-content-center.align-items-center {
    height: 100%;
    flex: 1;
  }

  .col-md-4 .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
  }

  .journey-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .journey-info .mb-4:nth-child(1),
  .journey-info .mb-4:nth-child(2) {
    flex-shrink: 0;
  }

  .journey-info .mb-4:nth-child(3) {
    min-height: 0;
    overflow-y: auto;
    padding-right: 5px;
  }

  /* Scrollbar styling */
  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar {
    width: 6px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* For Firefox */
  .journey-info .mb-4:nth-child(3) {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  .timeline-container {
    flex: 1;
    max-height: 560px !important;
    overflow-y: auto;
  }

  .timeline {
    position: relative;
    padding-left: 2rem;
  }

  .timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0.35rem;
    width: 2px;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
    height: 100%;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .timeline-date-marker {
    position: relative;
    margin-bottom: 1rem;
    margin-left: -0.5rem;
  }

  .timeline-date {
    margin-bottom: 0.5rem;
  }

  .timeline-content {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 0.5rem;
    width: 100%;
  }

  .timeline-content-wrapper {
    position: relative;
    overflow: visible;
  }


  .timeline-content.card {
    border: none !important;
    overflow: hidden;
  }

  .event-card-link {
    display: block;
    z-index: 1;
  }

  .timeline-image-container {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    overflow: hidden;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }


  .timeline-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    display: block;
  }

  .dropdown-menu {
    z-index: 1060 !important;
    overflow: visible;
    min-width: 10rem;
    background-color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, .15) !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .15) !important;
  }

  .dropdown-item {
    z-index: 1060 !important;
    position: relative;
  }

  .card {
    border-radius: 0.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .journey-info .text-uppercase {
    letter-spacing: 0.5px;
    font-size: 0.7rem;
  }

  .empty-state {
    max-width: 400px;
    padding: 2rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .empty-state-icon {
    margin: 0 auto;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  @media (max-width: 767.98px) {
    .timeline {
      padding-left: 1.5rem;
    }

    .timeline-image-container {
      width: 80px;
      height: 80px;
    }

    .timeline-image {
      width: 80px;
      height: 80px;
    }

    .col-md-4 .card-body {
      max-height: none;
    }

    .journey-info .mb-4:nth-child(3) {
      max-height: 150px;
    }

    .timeline-content-wrapper .event-card-link>div {
      border: none !important;
      box-shadow: none !important;
    }

    .timeline-content.card,
    .timeline-content .card-body {
      border: none !important;
      box-shadow: none !important;
    }
  }

  /* Cover image styling */
  .cover-image-container {
    margin-top: 15px;
  }

  .cover-image {
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    height: 200px;
    /* Fixed height for consistent display */
  }

  .cover-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .cover-image-placeholder {
    height: 200px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
  }

  .cover-image-controls {
    z-index: 5;
  }

  .cover-image:hover .cover-image-controls {
    opacity: 1;
  }

  /* Clickable cover image styling */
  .cover-image-clickable {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .cover-image-clickable:hover {
    transform: scale(1.02);
  }

  .cover-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
  }

  .cover-image-clickable:hover .cover-image-overlay {
    opacity: 1;
  }

  .cover-image-overlay i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .cover-image-overlay span {
    font-size: 0.875rem;
  }

  /* Simple Image modal styling */
  .image-modal .modal-dialog {
    max-width: 90vw;
    max-height: 90vh;
  }

  .image-modal .modal-content {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .image-modal .modal-header {
    border: none;
    padding: 1rem;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1050;
    background: none;
  }

  .image-modal .modal-title {
    display: none;
  }

  .image-modal .modal-body {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
  }

  .image-modal .modal-footer {
    display: none;
  }

  .image-modal img {
    max-width: 100%;
    max-height: 85vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .image-modal .modal-dialog {
      margin: 1rem;
      max-width: calc(100vw - 2rem);
    }

    .image-modal img {
      max-height: 80vh;
    }
  }

  /* Enhanced cover image controls */
  .cover-image-controls {
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 4px;
  }

  .cover-image-controls .btn {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
  }

  .cover-image-controls .btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  /* Loading state for cover image upload */
  .cover-image-uploading {
    position: relative;
    overflow: hidden;
  }

  .cover-image-uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .cover-image-uploading::before {
    content: 'Uploading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    z-index: 11;
  }
</style>
<script>
  // Global variables from server
  const JOURNEY_USER_ID = {{ journey.user_id }};
  const SESSION_USER_ID = {{ session.user_id if session.user_id else 'null' }};
  const IS_STAFF = {{ 'true' if session.get('role') in ['editor', 'admin', 'support_tech'] else 'false' }};

  // Simple and reliable back button function
  function smartBack() {
    // Method 1: Check for explicit back URL parameter (most reliable)
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      try {
        const decodedUrl = decodeURIComponent(backUrl);
        // Only allow internal URLs for security
        if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
          window.location.href = decodedUrl;
          return;
        }
      } catch (e) {
        // Invalid URL, continue to next method
      }
    }

    // Method 2: Simple history back (works most of the time)
    if (window.history.length > 1) {
      window.history.back();
      return;
    }

    // Method 3: Fallback to public journeys
    window.location.href = '{{ url_for("journey.get_public_journeys") }}';
  }


  // Simple back button text update
  function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');

    // Check for back URL parameter first
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      // Try to determine context from back URL
      if (backUrl.includes('/journey/public') || backUrl.includes('/discovery')) {
        backButtonText.textContent = 'Back to Discovery';
      } else if (backUrl.includes('/journey/private')) {
        backButtonText.textContent = 'Back to My Journeys';
      } else if (backUrl.includes('/journey/manage')) {
        backButtonText.textContent = 'Back to Management';
      } else if (backUrl.includes('/published_journey')) {
        backButtonText.textContent = 'Back to Published Journeys';
      } else {
        backButtonText.textContent = 'Back';
      }
      return;
    }

    // Fallback to simple referrer check
    const referrer = document.referrer;
    if (referrer && referrer.includes('/journey/public')) {
      backButtonText.textContent = 'Back to Discovery';
    } else if (referrer && referrer.includes('/journey/private')) {
      backButtonText.textContent = 'Back to My Journeys';
    } else if (referrer && referrer.includes('/journey/manage')) {
      backButtonText.textContent = 'Back to Management';
    } else {
      backButtonText.textContent = 'Back';
    }
  }

  document.addEventListener('DOMContentLoaded', function () {
    // Update back button text on page load
    updateBackButtonText();
    const mapIconWrapper = document.getElementById('mapIconWrapper');
    const mapPreview = document.getElementById('mapPreview');
    let hoverTimeout;

    // Show preview on hover
    mapIconWrapper.addEventListener('mouseenter', function () {
      clearTimeout(hoverTimeout);
      hoverTimeout = setTimeout(() => {
        mapPreview.classList.add('show');
      }, 200); // Small delay to prevent accidental triggers
    });

    // Hide preview when leaving both icon and preview
    function hidePreview() {
      clearTimeout(hoverTimeout);
      hoverTimeout = setTimeout(() => {
        if (!mapIconWrapper.matches(':hover') && !mapPreview.matches(':hover')) {
          mapPreview.classList.remove('show');
        }
      }, 100);
    }

    mapIconWrapper.addEventListener('mouseleave', hidePreview);
    mapPreview.addEventListener('mouseleave', hidePreview);

    // Keep preview visible when hovering over it
    mapPreview.addEventListener('mouseenter', function () {
      clearTimeout(hoverTimeout);
    });

    // Handle click to go to full map
    mapIconWrapper.addEventListener('click', function () {
      // This would navigate to your full map page
      console.log('Navigate to full map page');
      // window.location.href = '/journey/123/map';
    });

    mapPreview.addEventListener('click', function () {
      // This would also navigate to your full map page
      console.log('Navigate to full map page');
      // window.location.href = '/journey/123/map';
    });
  });

  // Map preview scripts:

  document.addEventListener('DOMContentLoaded', function () {
    const unsortedLocations = {{ locations | tojson | safe }};

    if (!unsortedLocations || unsortedLocations.length === 0) {
    console.warn("No valid locations provided.");
    return;
  }

  if (typeof L === 'undefined') {
    console.error("Leaflet library (L) is not loaded!");
    return;
  }

  const locations = unsortedLocations.sort((a, b) => {
    return new Date(a.event_start_datetime) - new Date(b.event_start_datetime);
  });

  const firstLat = parseFloat(locations[0].latitude);
  const firstLng = parseFloat(locations[0].longitude);

  const mymap = L.map('map').setView([firstLat, firstLng], 10);
  L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
  }).addTo(mymap);

  // Create a custom red icon
  const redIcon = L.icon({
    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  let pathPoints = [];

  // Group locations by coordinates
  const locationMap = locations.reduce((acc, loc) => {
    const key = `${loc.latitude},${loc.longitude}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(loc);
    return acc;
  }, {});

  // Carousel navigation function - moved outside to be globally accessible
  window.moveSlide = function (btn, direction) {
    const container = btn.closest('.carousel-container');
    const slides = Array.from(container.querySelectorAll('.carousel-slide'));
    let currentIndex = slides.findIndex(slide => slide.style.display === 'block');

    slides[currentIndex].style.display = 'none';
    const nextIndex = (currentIndex + direction + slides.length) % slides.length;
    slides[nextIndex].style.display = 'block';

    // Update the indicator
    const popup = btn.closest('.location-popup');
    const currentSlideEl = popup.querySelector('.current-slide');
    if (currentSlideEl) {
      currentSlideEl.textContent = nextIndex + 1;
    }
  };

  Object.entries(locationMap).forEach(([coordKey, locsAtSamePlace]) => {
    const [lat, lng] = coordKey.split(',').map(parseFloat);
    pathPoints.push([lat, lng]);

    const popupContent = `
          <div class="location-popup">
            ${locsAtSamePlace[0].name ? `<h3>${locsAtSamePlace[0].name}</h3>` : ''}
            <div class="carousel-container">
              <button class="carousel-button prev" onclick="moveSlide(this, -1)">&#10094;</button>
              <div class="carousel-content">
                ${locsAtSamePlace.map((loc, index) => `
                  <div class="carousel-slide" style="display: ${index === 0 ? 'block' : 'none'};">
                    <h5>Event</h5>
                    <h5 class="info-title">Title</h5>
                    <p>${loc.event_title}</p>
                    <h5 class="info-title">Description</h5>
                    <p>${loc.event_description || 'No description provided.'}</p>
                    <h5 class="info-title">Start Datetime</h5>
                    <p>${loc.start_datetime}</p>
                    <div class="d-flex justify-content-center">
                      <a href="/event/${loc.event_id}/detail" class="btn btn-dark rounded-pill px-4 text-white">View</a>
                    </div>
                  </div>
                `).join('')}
              </div>
              <button class="carousel-button next" onclick="moveSlide(this, 1)">&#10095;</button>
            </div>
            <div class="carousel-indicator">
              <span class="current-slide">1</span>/<span class="total-slides">${locsAtSamePlace.length}</span>
            </div>
          </div>
        `;

    const marker = L.marker([lat, lng], { icon: redIcon }).addTo(mymap);
    marker.bindPopup(popupContent, { maxWidth: 300 });
  });

  // Draw the path if we have at least 2 points
  if (pathPoints.length > 1) {
    const path = L.polyline(pathPoints, {
      color: 'red',
      weight: 3,
      opacity: 0.7,
      smoothFactor: 1
    }).addTo(mymap);

    // Zoom the map out to show all points
    const bounds = L.latLngBounds(pathPoints);
    mymap.fitBounds(bounds, {
      padding: [30, 30]
    });
  }
  });

  // edit event scripts
  document.querySelectorAll('.event-location-button').forEach(button => {
    button.addEventListener('click', async function (event) {
      event.preventDefault();
      const eventId = this.getAttribute('data-event-id');
      const response = await fetch(`/event/location/${eventId}`);
      const html = await response.text();

      showModal('', html, {
        hideCloseButton: true
      });

      setTimeout(() => {
        const mapElement = document.getElementById('map');
        if (!mapElement) {
          console.error("Map element not found after modal load");
          return;
        }

        const lat = parseFloat(mapElement.dataset.latitude);
        const lng = parseFloat(mapElement.dataset.longitude);
        if (typeof L === 'undefined' || isNaN(lat) || isNaN(lng)) {
          console.error("Leaflet not loaded or coordinates invalid");
          return;
        }

        // Initialize map
        const mymap = L.map('map').setView([lat, lng], 13);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(mymap);

        const redIcon = L.icon({
          iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41]
        });

        const marker = L.marker([lat, lng], { icon: redIcon }).addTo(mymap);
        marker.bindPopup(`<div class="location-popup"><h3>${mapElement.dataset.locationName || "Location"}</h3></div>`);

        // Invalidate the map after modal is fully shown
        const locationModal = document.getElementById('locationModal');
        locationModal.addEventListener('shown.bs.modal', () => {
          mymap.invalidateSize();
          console.log('Map size invalidated');
        });
      }, 200);
    });
  });



  function adjustTimelineLine() {
    const timeline = document.querySelector('.timeline');
    const container = document.querySelector('.timeline-container');
    if (timeline && container) {
      timeline.style.minHeight = '';
    }
  }
  window.addEventListener('load', adjustTimelineLine);
  window.addEventListener('resize', adjustTimelineLine);

  adjustTimelineLine();

  function confirmDeleteJourney(journeyId) {
    showModal('Delete Journey', 'Are you sure you want to delete this journey? This action cannot be undone.', {
      actionText: 'Delete',
      onAction: () => {
        const form = document.getElementById('deleteJourneyForm');
        form.submit();
      }
    });
  }

  document.addEventListener("click", function (event) {
    let locationInput = document.getElementById("location");
    let suggestionBox = document.getElementById("locationSuggestions");
    if (locationInput && suggestionBox && event.target !== locationInput && !suggestionBox.contains(event.target)) {
      suggestionBox.classList.add("d-none");
    }
  });

  document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".editEventBtn").forEach(button => {
      button.addEventListener("click", async function () {
        let eventId = this.getAttribute("data-event-id");
        const editUrl = `/event/${eventId}/edit`

        const response = await fetch(editUrl);
        const formHtml = await response.text();

        showModal('Edit Event', formHtml, {
          actionText: 'Save',
          onAction: async function () {
            const form = document.getElementById('editEventForm');
            form.classList.add('was-validated');

            const startDatetime = document.getElementById('startDatetime');
            const endDatetime = document.getElementById('endDatetime');
            const title = document.getElementById('title');
            const description = document.getElementById('description');
            const location = document.getElementById('location');
            const imageInput = document.getElementById('image');


            if (endDatetime && startDatetime.value && endDatetime.value) {
              if (new Date(endDatetime.value) <= new Date(startDatetime.value)) {
                endDatetime.setCustomValidity("End date must be after start date");
              } else {
                endDatetime.setCustomValidity("");
              }
            }

            if (imageInput && imageInput.files.length > 0) {
              if (imageInput.files[0].size > 5 * 1024 * 1024) {
                imageInput.setCustomValidity("File size exceeds 5MB");
              } else {
                imageInput.setCustomValidity("");
              }
            }

            if (!form.checkValidity()) {
              // Focus the first invalid field instead of showing browser popup
              const firstInvalidField = form.querySelector(':invalid');
              if (firstInvalidField) {
                firstInvalidField.focus();
              }
              return false; // Prevent modal from closing
            }

            const formData = new FormData(form);

            try {
              const response = await fetch(form.action, {
                method: 'POST',
                body: formData
              });

              if (response.redirected) {
                window.location.href = response.url;
              } else {
                window.location.reload();
              }
            } catch (error) {
              console.error("Error submitting form:", error);
              window.location.reload();
            }

            return true; // Allow modal to close

          }
        });
        setTimeout(() => {
          initMap();

          const lat = parseFloat(document.getElementById('latitude')?.value);
          const lng = parseFloat(document.getElementById('longitude')?.value);

          if (!isNaN(lat) && !isNaN(lng)) {

            if (typeof mymap !== 'undefined' && typeof locationMarker !== 'undefined') {
              mymap.setView([lat, lng], 13);
              locationMarker.setLatLng([lat, lng]);

              reverseGeocode(lat, lng);
            }
          }
        }, 200)
      });
    });
  });

  function confirmDeleteEvent(eventId) {
    showModal('Delete Event', 'Are you sure you want to delete this event? This action cannot be undone.', {
      actionText: 'Delete',
      onAction: () => {
        const form = document.getElementById(`deleteEventForm_${eventId}`);
        form.submit();
      }
    });
  }

  function confirmDeleteEventImage(eventId) {
    showModal('Delete Event Image', "Are you sure you want to delete this event's image? This action cannot be undone.", {
      actionText: 'Delete',
      onAction: () => {
        const form = document.getElementById(`deleteEventImageForm_${eventId}`);
        form.submit();
      }
    });
  }

  let mymap;
  let locationMarker;
  let redIcon;

  // Initialize the map when the modal is loaded
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.create-event-button').forEach(button => {
      button.addEventListener('click', async function (event) {
        event.preventDefault();
        console.log('🔧 Add Event button clicked - starting modal process');

        // Debug: Check if Bootstrap is loaded
        if (typeof bootstrap === 'undefined') {
          console.error('❌ Bootstrap is not loaded!');
          alert('Bootstrap is not loaded. Please refresh the page.');
          return;
        }

        // Debug: Check if showModal function exists
        if (typeof showModal !== 'function') {
          console.error('❌ showModal function is not available!');
          alert('Modal function is not available. Please refresh the page.');
          return;
        }

        console.log('✅ Bootstrap and showModal are available');

        try {
          console.log('🌐 Fetching create event form...');
          const response = await fetch(`/event/new/{{ journey.id }}`);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const formHtml = await response.text();
          console.log('✅ Form HTML fetched successfully');

          console.log('🔧 Opening modal...');
          showModal('Add New Event', formHtml, {
            actionText: 'Create',
            onAction: function () {
              const form = document.getElementById('createEventForm');
              if (!form) return false;

              // Basic validation
              form.classList.add('was-validated');

              if (!form.checkValidity()) {
                const firstInvalidField = form.querySelector(':invalid');
                if (firstInvalidField) {
                  firstInvalidField.focus();
                }
                return false;
              }

              // Check if location is selected
              const locationInput = document.getElementById('location');
              const latInput = document.getElementById('latitude');
              const lngInput = document.getElementById('longitude');

              if (!locationInput?.value || !latInput?.value || !lngInput?.value) {
                alert('Please select a location first.');
                return false;
              }

              // Submit the form
              form.submit();
              return true;
            }
          });

          // Execute scripts after modal content is loaded for new location selector
          setTimeout(() => {
            console.log('📝 Executing scripts from dynamically loaded content...');

            // Clean up any existing location selector instance
            if (window.locationSelector) {
              console.log('🧹 Cleaning up existing location selector...');
              // Clean up any existing event listeners or map instances
              if (window.locationSelector.map) {
                window.locationSelector.map.remove();
              }
              window.locationSelector = null;
            }

            // Extract and execute scripts from the loaded content
            const parser = new DOMParser();
            const doc = parser.parseFromString(formHtml, 'text/html');
            const scripts = doc.querySelectorAll('script');

            scripts.forEach((script, index) => {
              try {
                console.log(`📝 Executing script ${index + 1}/${scripts.length}`);

                // Create a new script element and copy the content
                const newScript = document.createElement('script');

                if (script.src) {
                  // External script
                  newScript.src = script.src;
                  newScript.onload = () => console.log(`✅ External script loaded: ${script.src}`);
                  newScript.onerror = () => console.error(`❌ Failed to load script: ${script.src}`);
                } else {
                  // Inline script - force re-initialization
                  newScript.textContent = script.textContent;
                }

                // Append to document head to execute
                document.head.appendChild(newScript);

                // Remove after execution to prevent duplicates
                setTimeout(() => {
                  if (newScript.parentNode) {
                    newScript.parentNode.removeChild(newScript);
                  }
                }, 100);

              } catch (error) {
                console.error(`❌ Error executing script ${index + 1}:`, error);
              }
            });

            // Force re-initialization of location selector after scripts execute
            setTimeout(() => {
              console.log('🔄 Force re-initializing location selector...');

              // Call the initialization function directly if it exists
              if (typeof window.initializeLocationSelector === 'function') {
                try {
                  window.initializeLocationSelector();
                  console.log('✅ Location selector re-initialized via function call');
                } catch (error) {
                  console.error('❌ Error re-initializing location selector:', error);
                }
              }

              // Verify initialization
              setTimeout(() => {
                if (window.locationSelector) {
                  console.log('✅ Location selector is ready');
                  console.log('Current state:', window.locationSelector.state);

                  // Test if search button is working
                  const searchBtn = document.getElementById('searchLocationBtn');
                  if (searchBtn) {
                    console.log('✅ Search button found:', searchBtn);
                  } else {
                    console.warn('⚠️ Search button not found');
                  }
                } else {
                  console.error('❌ Location selector still not available after re-initialization');
                }
              }, 100);
            }, 300);

          }, 100);

        } catch (error) {
          console.error('❌ Error in create event modal:', error);
          alert('Failed to load create event form. Please try again.');
        }
      });
    });
  });

  // Initialize the map
  function initMap() {
    const mapElement = document.getElementById('map');
    if (!mapElement) {
      console.error("Map element not found after modal load");
      return;
    }

    console.log("Map element found:", mapElement);


    // Default coordinates of Christchurch
    let initialLat = -43.5321
    let initialLng = 172.6362;


    const latInput = document.getElementById('latitude');
    const lngInput = document.getElementById('longitude');
    const locationNameInput = document.getElementById('location');

    if (latInput && lngInput && latInput.value && lngInput.value) {
      const parsedLat = parseFloat(latInput.value);
      const parsedLng = parseFloat(lngInput.value);

      if (!isNaN(parsedLat) && !isNaN(parsedLng)) {
        initialLat = parsedLat;
        initialLng = parsedLng;
        console.log(`Initializing map with event coords: ${initialLat}, ${initialLng}`);

        if (locationNameInput) {
          initialLocationName = locationNameInput.value;
        }
      } else {
        console.warn("Invalid initial latitude or longitude values:", latInput.value, lngInput.value);
      }
    } else {
      console.log("No existing event coordinates found, defaulting to Christchurch.");
    }


    if (typeof L === 'undefined') {
      console.error("Leaflet not loaded");
      return;
    }

    mymap = L.map('map').setView([initialLat, initialLng], 13);
    console.log(`Map initialized with view: [${initialLat}, ${initialLng}]`);

    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(mymap);

    // Create custom red icon
    redIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    });

    locationMarker = L.marker([initialLat, initialLng], { icon: redIcon, draggable: true }).addTo(mymap);

    // Update form when marker is dragged
    locationMarker.on('dragend', function (e) {
      const position = e.target.getLatLng();
      updateCoordinates(position.lat, position.lng);
      reverseGeocode(position.lat, position.lng);
    });

    // Update marker when map is clicked
    mymap.on('click', function (e) {
      locationMarker.setLatLng(e.latlng);
      updateCoordinates(e.latlng.lat, e.latlng.lng);
      reverseGeocode(e.latlng.lat, e.latlng.lng);
    });

    setupMapSearch();


    invalidateMapSize();
  }

  // Update hidden coordinates fields
  function updateCoordinates(lat, lng) {
    const latInput = document.getElementById('latitude');
    const lngInput = document.getElementById('longitude');

    if (latInput && lngInput) {
      latInput.value = lat;
      lngInput.value = lng;
    }
  }

  // Reverse geocode coordinates to address
  async function reverseGeocode(lat, lng) {
    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
      const data = await response.json();

      const locationMapInput = document.getElementById('location-map');
      if (locationMapInput) {
        locationMapInput.value = data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  }


  // Geocode address to coordinates
  async function geocode(address) {
    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`);
      const data = await response.json();

      if (data && data.length > 0) {
        const lat = parseFloat(data[0].lat);
        const lng = parseFloat(data[0].lon);

        mymap.setView([lat, lng], 13);
        locationMarker.setLatLng([lat, lng]);

        updateCoordinates(lat, lng);
      }
    } catch (error) {
      console.error('Error geocoding:', error);
    }
  }


  // Handle map location search
  function setupMapSearch() {
    const locationMapInput = document.getElementById('location-map');
    const mapSuggestions = document.getElementById('mapSuggestions');

    if (!locationMapInput || !mapSuggestions) return;

    let typingTimer;
    locationMapInput.addEventListener('input', function () {
      clearTimeout(typingTimer);
      const query = this.value.trim();

      if (query.length < 3) {
        mapSuggestions.classList.add('d-none');
        return;
      }

      typingTimer = setTimeout(function () {
        searchMapLocations(query);
      }, 500);
    });

    document.addEventListener('click', function (e) {
      if (!mapSuggestions.contains(e.target) && e.target !== locationMapInput) {
        mapSuggestions.classList.add('d-none');
      }
    });
  }

  // Search locations for the map input
  async function searchMapLocations(query) {
    const mapSuggestions = document.getElementById('mapSuggestions');

    if (!mapSuggestions) return;

    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5`);
      const data = await response.json();

      mapSuggestions.innerHTML = '';

      if (data.length === 0) {
        mapSuggestions.classList.add('d-none');
        return;
      }

      data.slice(0, 5).forEach(place => {
        const item = document.createElement('a');
        item.classList.add('list-group-item', 'list-group-item-action');
        item.textContent = place.display_name;
        item.href = '#';
        item.addEventListener('click', function (e) {
          e.preventDefault();
          selectMapLocation(place.display_name, place.lat, place.lon);
        });

        mapSuggestions.appendChild(item);
      });

      mapSuggestions.classList.remove('d-none');
    } catch (error) {
      console.error('Error searching locations:', error);
      mapSuggestions.classList.add('d-none');
    }
  }


  // Select a location for the map
  function selectMapLocation(placeName, lat, lng) {
    const locationMapInput = document.getElementById('location-map');
    const mapSuggestions = document.getElementById('mapSuggestions');

    if (locationMapInput) {
      locationMapInput.value = placeName;
    }

    if (mapSuggestions) {
      mapSuggestions.classList.add('d-none');
    }

    if (mymap && locationMarker) {
      lat = parseFloat(lat);
      lng = parseFloat(lng);

      mymap.setView([lat, lng], 13);
      locationMarker.setLatLng([lat, lng]);

      updateCoordinates(lat, lng);
    }
  }

  // Invalidate map size
  function invalidateMapSize() {
    if (!mymap) return;


    if (typeof bootstrap !== 'undefined') {
      const modalElement = document.querySelector('.modal');
      if (modalElement) {
        modalElement.addEventListener('shown.bs.modal', function () {
          mymap.invalidateSize();
          console.log('Map size invalidated after modal shown event');
        });
      }
    }

  }

  // Initialize location search functionality for modal content
  function initializeModalLocationSearch() {
    console.log('🔧 Initializing modal location search functionality');

    // Check if the location input exists in the modal
    const locationInput = document.getElementById('location');
    if (!locationInput) {
      console.warn('⚠️ Location input not found in modal');
      return;
    }

    console.log('✅ Found location input, setting up search functionality');

    // Enhanced location search function for modal
    async function modalSearchLocations() {
      console.log('🔍 modalSearchLocations() called (database search)');
      let query = locationInput.value.trim();
      let suggestionBox = document.getElementById('locationSuggestions');

      if (!suggestionBox) {
        console.warn('⚠️ locationSuggestions element not found');
        return;
      }

      if (query.length < 2) {
        hideModalLocationDropdown();
        return;
      }

      try {
        console.log(`🔍 Searching database for: "${query}"`);
        let response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
        let data = await response.json();
        suggestionBox.innerHTML = '';

        if (data.length === 0) {
          hideModalLocationDropdown();
          console.log('🔍 No database results found');
          return;
        }

        console.log(`🔍 Building ${data.length} database dropdown items`);
        data.forEach((location) => {
          let item = document.createElement('div');
          item.classList.add('list-group-item', 'list-group-item-action');
          item.textContent = location.name;

          item.onclick = async () => {
            console.log(`✅ Selected database location: ${location.name}`);
            locationInput.value = location.name;

            // Auto-fill coordinates and map from database location
            await modalLoadLocationCoordinates(location.name);

            hideModalLocationDropdown();

            // Add subtle animation feedback
            locationInput.style.transform = 'scale(1.02)';
            setTimeout(() => {
              locationInput.style.transform = 'scale(1)';
            }, 150);
          };

          suggestionBox.appendChild(item);
        });

        showModalLocationDropdown();
        console.log('✅ Database dropdown shown successfully');

      } catch (err) {
        console.error('❌ Error during location search:', err);
        hideModalLocationDropdown();
      }
    }

    // Load coordinates for a database location and update map
    async function modalLoadLocationCoordinates(locationName) {
      try {
        console.log(`🔍 Loading coordinates for: ${locationName}`);
        const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
        const data = await response.json();

        if (data.lat && data.lng) {
          console.log(`✅ Found coordinates: ${data.lat}, ${data.lng}`);

          // Update coordinate fields
          document.getElementById('latitude').value = data.lat;
          document.getElementById('longitude').value = data.lng;

          // Update map search field
          const mapInput = document.getElementById('location-map');
          if (mapInput) {
            mapInput.value = locationName;
          }

          // Update map if available
          if (mymap && locationMarker) {
            mymap.setView([data.lat, data.lng], 13);
            locationMarker.setLatLng([data.lat, data.lng]);
          }
        } else {
          console.log(`⚠️ No coordinates found for: ${locationName}`);
        }
      } catch (error) {
        console.error('❌ Error loading location coordinates:', error);
      }
    }

    // Show/hide dropdown functions for modal
    function showModalLocationDropdown() {
      const suggestionBox = document.getElementById('locationSuggestions');
      if (!suggestionBox) return;

      console.log('📍 Showing modal location dropdown');
      suggestionBox.classList.remove('d-none');
      suggestionBox.classList.add('show');
      suggestionBox.style.display = 'block';
      suggestionBox.style.visibility = 'visible';
      suggestionBox.style.opacity = '1';
    }

    function hideModalLocationDropdown() {
      const suggestionBox = document.getElementById('locationSuggestions');
      if (suggestionBox) {
        console.log('📍 Hiding modal location dropdown');
        suggestionBox.classList.add('d-none');
        suggestionBox.classList.remove('show');
        suggestionBox.style.display = 'none';
        suggestionBox.style.visibility = 'hidden';
        suggestionBox.style.opacity = '0';
      }
    }

    // Map search functionality for modal
    async function modalSearchMapLocations() {
      const mapInput = document.getElementById('location-map');
      const mapSuggestionBox = document.getElementById('mapSuggestions');

      if (!mapInput || !mapSuggestionBox) return;

      const query = mapInput.value.trim();
      if (query.length < 3) {
        hideModalMapDropdown();
        return;
      }

      try {
        console.log(`🗺️ Searching map locations for: "${query}"`);
        const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&addressdetails=1`);
        const data = await response.json();

        mapSuggestionBox.innerHTML = '';

        if (data.length === 0) {
          hideModalMapDropdown();
          return;
        }

        data.forEach((place, index) => {
          const item = document.createElement('div');
          item.classList.add('list-group-item', 'list-group-item-action');

          // Create content with "Most Relevant" label for first result
          const content = document.createElement('div');
          content.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
              <span>${place.display_name}</span>
              ${index === 0 ? '<small class="badge bg-success">Most Relevant</small>' : ''}
            </div>
          `;
          item.appendChild(content);
          item.style.cursor = 'pointer';

          item.onclick = async () => {
            console.log(`✅ Selected map location: ${place.display_name}`);

            const lat = parseFloat(place.lat);
            const lng = parseFloat(place.lon);

            // Update coordinate fields
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;

            // Enhanced UX: Auto-focus on map and prepopulate map search field
            mapInput.value = place.display_name;
            console.log(`✨ Auto-populated map search field: ${place.display_name}`);

            hideModalMapDropdown();

            // Auto-focus on the map section by scrolling to it
            const mapContainer = document.querySelector('.map-container');
            if (mapContainer) {
              mapContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });
              console.log('✨ Auto-focused on map section for better UX');
            }

            // Check for location name conflicts and handle accordingly
            const locationNameInput = document.getElementById('location');
            if (!locationNameInput.value.trim()) {
              // Auto-fill location name field if empty
              const parts = place.display_name.split(',');
              let cleanName = parts[0].trim();
              if (/^\d+$/.test(cleanName) && parts.length > 1) {
                cleanName = `${cleanName} ${parts[1].trim()}`;
              }
              locationNameInput.value = cleanName;
            } else {
              // Check if existing location name has different coordinates
              await modalCheckLocationConflict(locationNameInput.value, lat, lng, place.display_name);
            }

            // Update map if available
            if (mymap && locationMarker) {
              mymap.setView([lat, lng], 13);
              locationMarker.setLatLng([lat, lng]);
            }
          };

          mapSuggestionBox.appendChild(item);
        });

        showModalMapDropdown();

      } catch (error) {
        console.error('❌ Error searching map locations:', error);
        hideModalMapDropdown();
      }
    }

    // Show/hide map dropdown functions for modal
    function showModalMapDropdown() {
      const mapSuggestionBox = document.getElementById('mapSuggestions');
      if (!mapSuggestionBox) return;

      console.log('🗺️ Showing modal map dropdown');
      mapSuggestionBox.classList.remove('d-none');
      mapSuggestionBox.classList.add('show');
      mapSuggestionBox.style.display = 'block';
      mapSuggestionBox.style.visibility = 'visible';
      mapSuggestionBox.style.opacity = '1';
    }

    function hideModalMapDropdown() {
      const mapSuggestionBox = document.getElementById('mapSuggestions');
      if (mapSuggestionBox) {
        console.log('🗺️ Hiding modal map dropdown');
        mapSuggestionBox.classList.add('d-none');
        mapSuggestionBox.classList.remove('show');
        mapSuggestionBox.style.display = 'none';
        mapSuggestionBox.style.visibility = 'hidden';
        mapSuggestionBox.style.opacity = '0';
      }
    }

    // Attach event listeners for location name field
    locationInput.addEventListener('input', modalSearchLocations);
    locationInput.addEventListener('focus', function () {
      if (this.value.trim().length >= 2) {
        modalSearchLocations();
      }
    });

    // Attach event listeners for map search field
    const mapInput = document.getElementById('location-map');
    if (mapInput) {
      mapInput.addEventListener('input', modalSearchMapLocations);
      mapInput.addEventListener('focus', function () {
        if (this.value.trim().length >= 3) {
          modalSearchMapLocations();
        }
      });
    }

    // Check for location conflicts and show resolution dialog
    async function modalCheckLocationConflict(locationName, newLat, newLng, mapDisplayName) {
      try {
        console.log(`🔍 Checking conflict for: ${locationName}`);
        const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
        const data = await response.json();

        if (data.lat && data.lng) {
          const existingLat = parseFloat(data.lat);
          const existingLng = parseFloat(data.lng);

          // Calculate distance between coordinates (simple approximation)
          const distance = Math.sqrt(
            Math.pow(existingLat - newLat, 2) + Math.pow(existingLng - newLng, 2)
          ) * 111000; // Convert to meters (rough approximation)

          // If coordinates are significantly different (more than 1km apart)
          if (distance > 1000) {
            console.log(`⚠️ Location conflict detected! Distance: ${distance.toFixed(0)}m`);
            showModalLocationConflictDialog(locationName, existingLat, existingLng, newLat, newLng, mapDisplayName);
          } else {
            console.log(`✅ Coordinates are close enough (${distance.toFixed(0)}m apart)`);
          }
        }
      } catch (error) {
        console.error('❌ Error checking location conflict:', error);
      }
    }

    // Show location conflict resolution dialog for modal
    function showModalLocationConflictDialog(locationName, existingLat, existingLng, newLat, newLng, mapDisplayName) {
      // Calculate distance for display
      const distance = Math.sqrt(
        Math.pow(existingLat - newLat, 2) + Math.pow(existingLng - newLng, 2)
      ) * 111000; // Convert to meters

      const conflictHtml = `
        <div class="location-conflict-dialog">
          <div class="alert alert-warning mb-3">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Location Conflict Detected</strong>
          </div>

          <p class="mb-3">
            The location name "<strong>${locationName}</strong>" already exists in the database
            but points to a different location than what you selected on the map
            <span class="badge bg-warning text-dark">${distance > 1000 ? (distance / 1000).toFixed(1) + ' km' : distance.toFixed(0) + ' m'} apart</span>
          </p>

          <div class="row mb-3">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header bg-primary text-white">
                  <i class="bi bi-database me-2"></i>Existing Database Location
                </div>
                <div class="card-body">
                  <p><strong>Name:</strong> ${locationName}</p>
                  <p><strong>Location:</strong> <small class="text-muted">Lat: ${existingLat.toFixed(4)}, Lng: ${existingLng.toFixed(4)}</small></p>
                  <div class="mt-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.showConflictLocation(${existingLat}, ${existingLng}, 'existing')">
                      <i class="bi bi-geo-alt me-1"></i>Focus on Map
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-header bg-success text-white">
                  <i class="bi bi-geo-alt me-2"></i>Selected Map Location
                </div>
                <div class="card-body">
                  <p><strong>Name:</strong> ${mapDisplayName}</p>
                  <p><strong>Location:</strong> <small class="text-muted">Lat: ${newLat.toFixed(4)}, Lng: ${newLng.toFixed(4)}</small></p>
                  <div class="mt-2">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="window.showConflictLocation(${newLat}, ${newLng}, 'new')">
                      <i class="bi bi-geo-alt me-1"></i>Focus on Map
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Interactive Map showing both locations -->
          <div class="mb-3">
            <div class="card">
              <div class="card-header bg-light">
                <i class="bi bi-map me-2"></i>Location Comparison Map
                <small class="text-muted ms-2">Blue marker = Existing location, Green marker = Selected location</small>
              </div>
              <div class="card-body p-0">
                <div id="conflictMap" style="height: 300px; width: 100%;"></div>
              </div>
            </div>
          </div>

          <p class="mb-3"><strong>What would you like to do?</strong></p>

          <div class="d-grid gap-2">
            <button type="button" class="btn btn-primary" onclick="window.modalUseExistingLocation('${locationName}', ${existingLat}, ${existingLng})">
              <i class="bi bi-database me-2"></i>Keep "${locationName}" with its existing location
            </button>
            <button type="button" class="btn btn-success" onclick="window.modalCreateNewLocation('${mapDisplayName}', ${newLat}, ${newLng})">
              <i class="bi bi-plus-circle me-2"></i>Use new map location (will suggest new name)
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="window.modalCloseConflictDialog()">
              <i class="bi bi-x-circle me-2"></i>Cancel (no changes)
            </button>
          </div>
        </div>
      `;

      // Store the current modal content to restore later
      const currentModalBody = document.querySelector('#commonModal .modal-body').innerHTML;
      const currentModalTitle = document.querySelector('#commonModal .modal-title').textContent;

      // Store for restoration
      window.previousModalContent = {
        title: currentModalTitle,
        body: currentModalBody
      };

      // Show conflict dialog in the same modal
      const conflictModal = document.getElementById('commonModal');
      const modalTitle = conflictModal.querySelector('.modal-title');
      const modalBody = conflictModal.querySelector('.modal-body');

      modalTitle.textContent = 'Location Conflict';
      modalBody.innerHTML = conflictHtml;

      // Hide action button and close button for conflict dialog
      const actionBtn = document.getElementById('modalActionBtn');
      const closeBtn = modal.querySelector('.close-button');
      if (actionBtn) actionBtn.style.display = 'none';
      if (closeBtn) closeBtn.style.display = 'none';

      // Initialize the conflict map after a short delay to ensure DOM is ready
      setTimeout(() => {
        window.initializeConflictMap(existingLat, existingLng, newLat, newLng, locationName, mapDisplayName);
      }, 200);
    }

    // Initialize conflict comparison map
    window.initializeConflictMap = function (existingLat, existingLng, newLat, newLng, existingName, newName) {
      try {
        console.log('🗺️ Initializing conflict comparison map');

        // Remove existing map if any
        if (window.conflictMap) {
          window.conflictMap.remove();
          window.conflictMap = null;
        }

        // Calculate center point between the two locations
        const centerLat = (existingLat + newLat) / 2;
        const centerLng = (existingLng + newLng) / 2;

        // Calculate appropriate zoom level based on distance
        const distance = Math.sqrt(
          Math.pow(existingLat - newLat, 2) + Math.pow(existingLng - newLng, 2)
        ) * 111000; // Convert to meters

        let zoomLevel = 13;
        if (distance > 10000) zoomLevel = 10;      // > 10km
        else if (distance > 5000) zoomLevel = 11;  // > 5km
        else if (distance > 2000) zoomLevel = 12;  // > 2km
        else if (distance > 500) zoomLevel = 14;   // > 500m
        else zoomLevel = 15;                       // < 500m

        // Initialize the map
        window.conflictMap = L.map('conflictMap').setView([centerLat, centerLng], zoomLevel);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors'
        }).addTo(window.conflictMap);

        // Create custom icons
        const existingIcon = L.divIcon({
          className: 'custom-marker existing-marker',
          html: '<div style="background-color: #0d6efd; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><i class="bi bi-database" style="font-size: 12px;"></i></div>',
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        });

        const newIcon = L.divIcon({
          className: 'custom-marker new-marker',
          html: '<div style="background-color: #198754; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><i class="bi bi-geo-alt" style="font-size: 12px;"></i></div>',
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        });

        // Add markers
        window.existingMarker = L.marker([existingLat, existingLng], { icon: existingIcon })
          .addTo(window.conflictMap)
          .bindPopup(`<strong>Existing Location</strong><br>${existingName}<br><small>Lat: ${existingLat.toFixed(4)}, Lng: ${existingLng.toFixed(4)}</small>`);

        window.newMarker = L.marker([newLat, newLng], { icon: newIcon })
          .addTo(window.conflictMap)
          .bindPopup(`<strong>Selected Location</strong><br>${newName}<br><small>Lat: ${newLat.toFixed(4)}, Lng: ${newLng.toFixed(4)}</small>`);

        // Add a line connecting the two points
        const connectionLine = L.polyline([[existingLat, existingLng], [newLat, newLng]], {
          color: '#ffc107',
          weight: 3,
          opacity: 0.7,
          dashArray: '10, 10'
        }).addTo(window.conflictMap);

        // Add distance label at midpoint
        const midLat = (existingLat + newLat) / 2;
        const midLng = (existingLng + newLng) / 2;
        const distanceText = distance > 1000 ? `${(distance / 1000).toFixed(1)} km` : `${distance.toFixed(0)} m`;

        L.marker([midLat, midLng], {
          icon: L.divIcon({
            className: 'distance-label',
            html: `<div style="background: #ffc107; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: bold; border: 1px solid #fff; box-shadow: 0 1px 2px rgba(0,0,0,0.3);">${distanceText} apart</div>`,
            iconSize: [60, 20],
            iconAnchor: [30, 10]
          })
        }).addTo(window.conflictMap);

        console.log('✅ Conflict map initialized successfully');

      } catch (error) {
        console.error('❌ Error initializing conflict map:', error);
      }
    };

    // Function to focus on specific location in conflict map
    window.showConflictLocation = function (lat, lng, type) {
      if (window.conflictMap) {
        window.conflictMap.setView([lat, lng], 16);

        // Open the appropriate popup
        if (type === 'existing' && window.existingMarker) {
          window.existingMarker.openPopup();
        } else if (type === 'new' && window.newMarker) {
          window.newMarker.openPopup();
        }

        console.log(`🎯 Focused on ${type} location: ${lat}, ${lng}`);
      }
    };

    // Function to show location on map (for preview buttons)
    window.showLocationOnMap = function (lat, lng, type) {
      if (mymap && locationMarker) {
        mymap.setView([lat, lng], 15);
        locationMarker.setLatLng([lat, lng]);

        // Add a temporary popup to show which location is being previewed
        const popupText = type === 'existing' ? 'Existing Database Location' : 'Selected Map Location';
        locationMarker.bindPopup(popupText).openPopup();

        // Remove popup after 2 seconds
        setTimeout(() => {
          locationMarker.closePopup();
        }, 2000);
      }
    };

    // Handle conflict resolution choices - make them globally accessible
    window.modalUseExistingLocation = function (locationName, lat, lng) {
      console.log(`✅ Using existing location: ${locationName}`);

      // Restore original modal content first
      window.modalRestoreOriginalContent();

      // Then update the form fields in the restored content
      setTimeout(() => {
        // Update location name field to the existing location name
        const locationNameInput = document.getElementById('location');
        if (locationNameInput) {
          locationNameInput.value = locationName;
          console.log(`📍 Location name field updated to: ${locationName}`);
        }

        // Update coordinates to existing location
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');
        if (latInput && lngInput) {
          latInput.value = lat;
          lngInput.value = lng;
          console.log(`📍 Coordinates updated to: ${lat}, ${lng}`);
        }

        // Update map search field
        const mapInput = document.getElementById('location-map');
        if (mapInput) {
          mapInput.value = locationName;
          console.log(`🗺️ Map search field updated to: ${locationName}`);
        }

        // Update map if available
        if (mymap && locationMarker) {
          mymap.setView([lat, lng], 13);
          locationMarker.setLatLng([lat, lng]);
          console.log(`🗺️ Map updated to show: ${lat}, ${lng}`);
        }

        // Re-initialize the modal functionality
        initializeModalLocationSearch();

        // Show success feedback
        if (locationNameInput) {
          locationNameInput.style.backgroundColor = '#d4edda';
          setTimeout(() => {
            locationNameInput.style.backgroundColor = '';
          }, 1500);
        }
      }, 100);
    };

    window.modalCreateNewLocation = function (mapDisplayName, lat, lng) {
      console.log(`✅ Creating new location: ${mapDisplayName}`);

      // Extract clean name from map display name as suggestion
      const parts = mapDisplayName.split(',');
      let suggestedName = parts[0].trim();
      if (/^\d+$/.test(suggestedName) && parts.length > 1) {
        suggestedName = `${suggestedName} ${parts[1].trim()}`;
      }

      // Restore original modal content first
      window.modalRestoreOriginalContent();

      // Then update the form fields in the restored content
      setTimeout(() => {
        // Update coordinates to new map location
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');
        if (latInput && lngInput) {
          latInput.value = lat;
          lngInput.value = lng;
          console.log(`📍 Coordinates updated to new location: ${lat}, ${lng}`);
        }

        // Update map search field
        const mapInput = document.getElementById('location-map');
        if (mapInput) {
          mapInput.value = mapDisplayName;
          console.log(`🗺️ Map search field updated to: ${mapDisplayName}`);
        }

        // Update map if available
        if (mymap && locationMarker) {
          mymap.setView([lat, lng], 13);
          locationMarker.setLatLng([lat, lng]);
          console.log(`🗺️ Map updated to show new location: ${lat}, ${lng}`);
        }

        // Auto-fill location name field with suggested name and focus it for editing
        const locationNameInput = document.getElementById('location');
        if (locationNameInput) {
          locationNameInput.value = suggestedName;
          locationNameInput.focus();
          locationNameInput.select(); // Select all text so user can easily replace if needed

          // Add visual emphasis to indicate this is a suggested name
          locationNameInput.style.backgroundColor = '#e7f3ff';
          locationNameInput.style.borderColor = '#0066cc';

          // Add helper text
          const helpText = document.createElement('div');
          helpText.className = 'form-text text-info mt-1';
          helpText.innerHTML = `<i class="bi bi-info-circle me-1"></i>Suggested name auto-filled. You can edit it to make it unique if needed.`;
          helpText.id = 'location-help-text';

          // Remove existing help text if any
          const existingHelp = document.getElementById('location-help-text');
          if (existingHelp) {
            existingHelp.remove();
          }

          // Add help text after the input
          locationNameInput.parentNode.insertBefore(helpText, locationNameInput.nextSibling);

          // Remove styling and help text when user starts typing
          locationNameInput.addEventListener('input', function removeHelpers() {
            this.style.backgroundColor = '';
            this.style.borderColor = '';

            const helpElement = document.getElementById('location-help-text');
            if (helpElement) {
              helpElement.remove();
            }

            // Remove this event listener after first use
            this.removeEventListener('input', removeHelpers);
          });

          console.log(`📍 Location name field auto-filled with suggested name: ${suggestedName}`);
        }

        // Re-initialize the modal functionality
        initializeModalLocationSearch();
      }, 100);
    };

    window.modalCloseConflictDialog = function () {
      console.log('🚫 Canceling conflict resolution');

      // Restore original modal content
      window.modalRestoreOriginalContent();

      // Re-initialize the modal functionality
      setTimeout(() => {
        initializeModalLocationSearch();
      }, 100);
    };

    // Function to restore original modal content
    window.modalRestoreOriginalContent = function () {
      if (window.previousModalContent) {
        // Clean up conflict map if it exists
        if (window.conflictMap) {
          console.log('🗺️ Cleaning up conflict map');
          window.conflictMap.remove();
          window.conflictMap = null;
          window.existingMarker = null;
          window.newMarker = null;
        }

        const modal = document.getElementById('commonModal');
        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        const actionBtn = document.getElementById('modalActionBtn');
        const closeBtn = modal.querySelector('.close-button');

        // Restore content
        modalTitle.textContent = window.previousModalContent.title;
        modalBody.innerHTML = window.previousModalContent.body;

        // Restore buttons
        if (actionBtn) actionBtn.style.display = 'block';
        if (closeBtn) closeBtn.style.display = 'block';

        // Clear stored content
        window.previousModalContent = null;

        console.log('✅ Original modal content restored');
      }
    };

    // Click outside handler for modal
    document.addEventListener('click', function (event) {
      const suggestionBox = document.getElementById('locationSuggestions');
      if (locationInput && suggestionBox &&
        event.target !== locationInput &&
        !suggestionBox.contains(event.target)) {
        hideModalLocationDropdown();
      }
    });

    console.log('✅ Modal location search functionality initialized');
  }

  // Function to show protected journey message
  function showProtectedJourneyMessage() {
    showModal('Journey Protected',
      '<div class="text-center">' +
      '<i class="bi bi-shield-lock text-warning" style="font-size: 3rem;"></i>' +
      '<h5 class="mt-3 mb-3">This Journey is Protected</h5>' +
      '<p class="text-muted">This journey has been protected by its owner from staff edits. ' +
      'You can still hide the journey if needed for moderation purposes.</p>' +
      '</div>'
    );
  }

  // Journey Map initialization
  document.addEventListener('DOMContentLoaded', function () {
    const journeyMapElement = document.getElementById('journeyMap');
    if (!journeyMapElement) return;

    const unsortedLocations = {{ locations | tojson | safe }};

    if (!unsortedLocations || unsortedLocations.length === 0) {
    console.warn("No valid locations provided for journey map.");
    return;
  }

  if (typeof L === 'undefined') {
    console.error("Leaflet library (L) is not loaded!");
    return;
  }

  // Initialize the journey map
  initializeJourneyMap(unsortedLocations);
  });

  function initializeJourneyMap(unsortedLocations) {
    const journeyMapElement = document.getElementById('journeyMap');
    if (!journeyMapElement) return;

    const locations = unsortedLocations.sort((a, b) => {
      return new Date(a.event_start_datetime) - new Date(b.event_start_datetime);
    });

    const firstLat = parseFloat(locations[0].latitude);
    const firstLng = parseFloat(locations[0].longitude);

    const journeyMap = L.map('journeyMap', {
      zoomControl: true,
      scrollWheelZoom: true
    }).setView([firstLat, firstLng], 10);

    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(journeyMap);

    // Create a custom red icon
    const redIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [20, 32],
      iconAnchor: [10, 32],
      popupAnchor: [1, -28],
      shadowSize: [32, 32]
    });

    let pathPoints = [];

    // Group locations by coordinates
    const locationMap = locations.reduce((acc, loc) => {
      const key = `${loc.latitude},${loc.longitude}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(loc);
      return acc;
    }, {});

    Object.entries(locationMap).forEach(([coordKey, locsAtSamePlace]) => {
      const [lat, lng] = coordKey.split(',').map(parseFloat);
      pathPoints.push([lat, lng]);

      // Create popup content
      const popupContent = locsAtSamePlace.length === 1 ?
        `<div class="event-popup">
          <h6>${locsAtSamePlace[0].event_title}</h6>
          <p class="mb-1"><small><strong>Location:</strong> ${locsAtSamePlace[0].location_name}</small></p>
          <p class="mb-2"><small><strong>Date:</strong> ${new Date(locsAtSamePlace[0].event_start_datetime).toLocaleDateString()}</small></p>
          <a href="/event/${locsAtSamePlace[0].event_id}/detail" class="btn btn-sm btn-primary">View Event</a>
        </div>` :
        `<div class="location-popup">
          <h6>${locsAtSamePlace[0].location_name}</h6>
          <p class="mb-2"><small>${locsAtSamePlace.length} events at this location</small></p>
          ${locsAtSamePlace.slice(0, 2).map(loc => `
            <div class="mb-2">
              <strong>${loc.event_title}</strong><br>
              <small>${new Date(loc.event_start_datetime).toLocaleDateString()}</small>
            </div>
          `).join('')}
          ${locsAtSamePlace.length > 2 ? `<small>...and ${locsAtSamePlace.length - 2} more</small>` : ''}
        </div>`;

      const marker = L.marker([lat, lng], { icon: redIcon }).addTo(journeyMap);
      marker.bindPopup(popupContent, { maxWidth: 250 });
    });

    // Draw the path if we have at least 2 points
    if (pathPoints.length > 1) {
      const path = L.polyline(pathPoints, {
        color: '#dc3545',
        weight: 3,
        opacity: 0.7,
        smoothFactor: 1
      }).addTo(journeyMap);

      // Zoom the map out to show all points
      const bounds = L.latLngBounds(pathPoints);
      journeyMap.fitBounds(bounds, {
        padding: [20, 20]
      });
    }

    // Invalidate size after a short delay to ensure proper rendering
    setTimeout(() => {
      journeyMap.invalidateSize();
    }, 100);
  }

  document.addEventListener('DOMContentLoaded', function () {
    // Debug: Track appeal section visibility
    function checkAppealSection() {
      const appealSection = document.getElementById('appealSection');
      const rejectionReason = document.getElementById('rejectionReason');
      console.log('Appeal Section Check:', {
        appealExists: !!appealSection,
        appealVisible: appealSection ? appealSection.style.display !== 'none' : false,
        rejectionExists: !!rejectionReason,
        rejectionVisible: rejectionReason ? rejectionReason.style.display !== 'none' : false,
        appealStatus: {{ appeal_status | tojson if appeal_status else 'null' }}
      });
    }

    // Check appeal section on page load
    checkAppealSection();

    // Edit Journey button event listener
    document.getElementById('editJourneyBtn')?.addEventListener('click', async function (event) {
    event.preventDefault();
    console.log('🔧 Edit Journey button clicked - starting modal process');

    // Debug: Check if Bootstrap is loaded
    if (typeof bootstrap === 'undefined') {
      console.error('❌ Bootstrap is not loaded!');
      alert('Bootstrap is not loaded. Please refresh the page.');
      return;
    }

    // Debug: Check if showModal function exists
    if (typeof showModal !== 'function') {
      console.error('❌ showModal function is not available!');
      alert('Modal function is not available. Please refresh the page.');
      return;
    }

    console.log('✅ Bootstrap and showModal are available');
    checkAppealSection();

    try {
      console.log('🌐 Fetching journey edit form...');
      const response = await fetch("{{ url_for('journey.get_journey_edit_form', journey_id=journey.id) }}");

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const formHtml = await response.text();
      console.log('✅ Form HTML fetched successfully');

      console.log('🔧 Opening modal...');
      showModal('Edit Journey', formHtml, {
      actionText: 'Save',
      onAction: async function () {
        const form = document.getElementById('editJourneyForm');
        form.classList.add('was-validated');

        if (!form.checkValidity()) {
          // Focus the first invalid field instead of showing browser popup
          const firstInvalidField = form.querySelector(':invalid');
          if (firstInvalidField) {
            firstInvalidField.focus();
          }
          return false; // Prevent modal from closing
        }

        // Submit form via AJAX
        const journeyId = {{ journey.id }};
        const formData = new FormData(form);

        try {
          const response = await fetch(`/journey/private/${journeyId}/edit`, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          });

          if (response.ok) {
            // Close the modal first
            const modalElement = document.getElementById('commonModal');
            if (modalElement) {
              const modalInstance = bootstrap.Modal.getInstance(modalElement);
              if (modalInstance) {
                modalInstance.hide();
              }
            }

            // Parse JSON response
            const data = await response.json();

            // Store the message for display after redirect
            const messageType = data.success ? 'success' : 'danger';
            storeFlashMessage(data.message, messageType);

            // Redirect to the specified URL
            window.location.href = data.redirect_url;
          } else {
            const errorText = await response.text();
            console.error('Error updating journey:', errorText);
            alert('Error updating journey. Please try again.');
          }
        } catch (error) {
          console.error('Error:', error);
          alert('Error updating journey. Please try again.');
        }

        return true; // Allow modal to close
      }
    });

      // Debug: Add modal close event listener
      const modal = document.getElementById('commonModal');
      modal.addEventListener('hidden.bs.modal', function onModalHidden() {
        console.log('Edit Journey modal closed - checking appeal section after modal close');
        setTimeout(() => {
          checkAppealSection();
        }, 100); // Small delay to ensure DOM is updated

        // Remove this specific listener to avoid duplicates
        modal.removeEventListener('hidden.bs.modal', onModalHidden);
      });

    } catch (error) {
      console.error('❌ Error in edit journey modal:', error);
      alert('Failed to load edit form. Please try again.');
    }
  });
});

// Cover image management
setupCoverImageManagement();

// Modal functionality is working correctly

function setupCoverImageManagement() {
    const addBtn = document.getElementById('addCoverImageBtn');
    const changeBtn = document.getElementById('changeCoverImageBtn');
    const removeBtn = document.getElementById('removeCoverImageBtn');
    const fileInput = document.getElementById('coverImageInput');

    // Add cover image
    addBtn?.addEventListener('click', function () {
      fileInput?.click();
    });

    // Change cover image
    changeBtn?.addEventListener('click', function () {
      fileInput?.click();
    });

    // Remove cover image
    removeBtn?.addEventListener('click', function () {
      const isOwner = (JOURNEY_USER_ID === SESSION_USER_ID);
      const isStaff = IS_STAFF;

      // If staff is removing someone else's cover image, require edit reason
      if (isStaff && !isOwner) {
    const editReasonForm = `
          <div class="mb-3">
            <label for="editReason" class="form-label">Reason for Removal *</label>
            <textarea class="form-control" id="editReason" rows="3" required
              placeholder="Please provide a reason for removing this cover image..."></textarea>
            <div class="invalid-feedback">Staff must provide a reason for removing user content</div>
          </div>
          <p class="text-muted small">As a staff member, you must provide a reason when removing user content.</p>
        `;

    showModal('Remove Cover Image', editReasonForm, {
      actionText: 'Remove',
      onAction: async function () {
        const editReason = document.getElementById('editReason').value.trim();
        if (!editReason) {
          document.getElementById('editReason').classList.add('is-invalid');
          return false; // Prevent modal from closing
        }

        try {
          const formData = new FormData();
          formData.append('edit_reason', editReason);

          const response = await fetch("{{ url_for('journey.remove_cover_image', journey_id=journey.id) }}", {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          });

          // Check if response is JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            // Parse JSON response
            const data = await response.json();

            // Store flash message for display after page reload
            if (data.message) {
              storeFlashMessage(data.message, data.success ? 'success' : 'danger');
            }
          } else {
            // If not JSON, it might be a redirect response
            storeFlashMessage('Cover image removed successfully', 'success');
          }

          // Reload the page to show the updated cover image
          window.location.reload();
        } catch (error) {
          console.error('Error removing cover image:', error);
          storeFlashMessage('Failed to remove cover image. Please try again.', 'danger');
          window.location.reload();
        }
      }
    });
  } else {
    // Owner removing their own cover image - no edit reason needed
    showModal('Remove Cover Image', 'Are you sure you want to remove this cover image? This action cannot be undone.', {
      actionText: 'Remove',
      onAction: async function () {
        try {
          const response = await fetch("{{ url_for('journey.remove_cover_image', journey_id=journey.id) }}", {
            method: 'POST',
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          });

          // Check if response is JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            // Parse JSON response
            const data = await response.json();

            // Store flash message for display after page reload
            if (data.message) {
              storeFlashMessage(data.message, data.success ? 'success' : 'danger');
            }
          } else {
            // If not JSON, it might be a redirect response
            storeFlashMessage('Cover image removed successfully', 'success');
          }

          // Reload the page to show the updated cover image
          window.location.reload();
        } catch (error) {
          console.error('Error removing cover image:', error);
          storeFlashMessage('Failed to remove cover image. Please try again.', 'danger');
          window.location.reload();
        }
      }
    });
      }
    });

    // Handle file selection
    fileInput?.addEventListener('change', function (event) {
      const file = event.target.files[0];
      if (file) {
        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
          showFlashMessage('File size exceeds 5MB limit. Please choose a smaller file.', 'danger');
          return;
        }

        // Validate file type
        const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
          showFlashMessage('Invalid file type. Please choose a PNG, JPG, JPEG, or GIF file.', 'danger');
          return;
        }

        uploadCoverImage(file);
      }
    });
  }

  async function uploadCoverImage(file) {
    const formData = new FormData();
    formData.append('image', file);

    // Show loading state
    const container = document.getElementById('coverImageContainer') || document.getElementById('coverImagePlaceholder');
    if (container) {
      container.classList.add('cover-image-uploading');
    }

    try {
      const response = await fetch("{{ url_for('journey.upload_cover_image', journey_id=journey.id) }}", {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
      });

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // Parse JSON response
        const data = await response.json();

        // Store flash message for display after page reload
        if (data.message) {
          storeFlashMessage(data.message, data.success ? 'success' : 'danger');
        }
      } else {
        // If not JSON, it might be a redirect response
        storeFlashMessage('Cover image uploaded successfully', 'success');
      }

      // Reload the page to show the updated cover image
      window.location.reload();
    } catch (error) {
      console.error('Error uploading cover image:', error);
      storeFlashMessage('Failed to upload cover image. Please try again.', 'danger');

      // Remove loading state on error
      if (container) {
        container.classList.remove('cover-image-uploading');
      }
    }
  }

  // Legacy function for backward compatibility
  async function uploadCoverPhoto(file) {
    return uploadCoverImage(file);
  }

  // Simple Bootstrap modal for cover image
  function showCoverImageModal(imageUrl, journeyTitle) {
    const modalContent = `
      <div class="text-center">
        <img src="${imageUrl}" alt="Cover image for ${journeyTitle}" class="img-fluid">
      </div>
    `;

    showModal('', modalContent);

    // Add image-modal class for minimal styling
    const modal = document.getElementById('commonModal');
    modal.classList.add('image-modal');

    // Clean up when modal closes
    modal.addEventListener('hidden.bs.modal', function onHidden() {
      modal.removeEventListener('hidden.bs.modal', onHidden);
      modal.classList.remove('image-modal');
    });
  }


  //  EVENT UPDATE SCRIPTS:
  let newFiles = [];
  let deletedImageIds = [];
  const maxFiles = 10;

  document.addEventListener('DOMContentLoaded', function () {

    // Legacy edit event form validation - now handled by standard form validation system
    // This code is kept for backward compatibility but the form validation
    // is now handled by the global form-validation.js system
  });


  function handleFilesSelected(files) {
    const currentTotalFiles = document.querySelectorAll('#current-images .image-card').length - deletedImageIds.length + newFiles.length;
    if (currentTotalFiles + files.length > maxFiles) {
      alert(`You can upload a maximum of ${maxFiles} images in total (including existing ones not marked for deletion).`);
      document.getElementById('newImages').value = '';
      return;
    }

    Array.from(files).forEach(file => {
      if (!file.type.match('image.*')) {
        console.warn('Skipping non-image file:', file.name);
        return;
      }

      if (newFiles.some(existingFile => existingFile.file.name === file.name && existingFile.file.size === file.size)) {
        console.warn('Skipping duplicate file:', file.name);
        return;
      }

      const newIndex = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      newFiles.push({
        file: file,
        index: newIndex
      });
    });

    renderNewImages();
  }

  function renderNewImages() {
    const container = document.getElementById('new-images');
    container.innerHTML = '';

    newFiles.forEach((item) => {
      const reader = new FileReader();

      reader.onload = function (e) {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'col-6 col-md-4';
        imageDiv.id = `new-image-${item.index}`;
        imageDiv.innerHTML = `
      <div class="card h-100 position-relative image-card">
        <div class="image-wrapper">
          <img src="${e.target.result}" class="preview-image card-img-top" alt="Preview">
          <div class="image-controls">
            <button type="button" class="btn btn-sm btn-light"
                    onclick="removeNewImage('${item.index}')">
              <i class="bi bi-x "></i>
            </button>
          </div>
        </div>
        <div class="card-body p-1" style="min-height: 10px;"> <input type="hidden" name="new_image_index[]" value="${item.index}">
            </div>
      </div>
    `;
        container.appendChild(imageDiv);
      };

      reader.readAsDataURL(item.file);
    });

    updateFileInput();
  }

  function updateFileInput() {
    const dt = new DataTransfer();
    newFiles.forEach(item => {
      dt.items.add(item.file);
    });
    document.getElementById('newImages').files = dt.files;
  }

  function removeNewImage(index) {
    const arrayIndex = newFiles.findIndex(item => item.index === index);
    if (arrayIndex === -1) return;

    newFiles.splice(arrayIndex, 1);

    const element = document.getElementById(`new-image-${index}`);
    if (element) {
      element.remove();
    }

    updateFileInput();
  }

  function removeExistingImage(id) {
    const imageElement = document.getElementById(`existing-image-${id}`);
    const removeButton = imageElement.querySelector('.image-controls .delete-button');
    const undoButton = imageElement.querySelector('.image-controls .undo-button');

    removeButton.classList.add('d-none');
    undoButton.classList.remove('d-none');

    if (!deletedImageIds.includes(id)) {
      deletedImageIds.push(id);
      document.getElementById('deletedImages').value = deletedImageIds.join(',');
    }


    if (imageElement) {
      imageElement.querySelector('.image-card').classList.add('deleted-image');

      let overlay = imageElement.querySelector('.image-x-overlay');
      if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'image-x-overlay';

        const imageWrapper = imageElement.querySelector('.image-wrapper');
        imageWrapper.appendChild(overlay);
      }

      const deleteButton = imageElement.querySelector('.image-controls button');
      if (deleteButton) {
        deleteButton.disabled = true;
      }
    }
  }

  function undoRemoveImage(id) {
    const imageElement = document.getElementById(`existing-image-${id}`);
    const removeButton = imageElement.querySelector('.image-controls .btn');
    const undoButton = imageElement.querySelector('.image-controls .undo-button');

    removeButton.classList.remove('d-none');
    undoButton.classList.add('d-none');

    imageElement.querySelector('.image-card').classList.remove('deleted-image');
    const overlay = imageElement.querySelector('.image-x-overlay');
    if (overlay) {
      overlay.remove();
    }

    const deleteButton = imageElement.querySelector('.image-controls button');
    if (deleteButton) {
      deleteButton.disabled = false;
    }

    const index = deletedImageIds.indexOf(id);
    if (index !== -1) {
      deletedImageIds.splice(index, 1);
      document.getElementById('deletedImages').value = deletedImageIds.join(',');
    }
  }


  function validateFileCount(input) {
    const maxFiles = 5;
    const files = input.files;

    if (files.length > maxFiles) {
      input.value = '';
      input.classList.add('is-invalid');
      document.getElementById('imagesFeedback').textContent = `You can only upload up to ${maxFiles} images.`;
      return false;
    }
  }

  function previewImage(input) {
    const preview = document.getElementById('current-image-preview');
    const container = document.getElementById('image-preview-container');

    if (input.files && input.files[0]) {
      const reader = new FileReader();

      reader.onload = function (e) {
        if (preview) {
          preview.src = e.target.result;
        } else {
          const img = document.createElement('img');
          img.id = 'current-image-preview';
          img.src = e.target.result;
          img.className = 'img-fluid rounded w-100';
          img.style.maxHeight = '300px';
          img.style.objectFit = 'cover';

          // Clear placeholder and add the image
          container.innerHTML = '';
          container.appendChild(img);
        }
      };

      reader.readAsDataURL(input.files[0]);
    }
  }

  document.getElementById('images').addEventListener('change', function (e) {
    const isPremium = this.dataset.premium === 'true';
    const maxFiles = isPremium ? 10 : 1;
    const files = e.target.files;
    const feedback = document.getElementById('imagesFeedback');

    if (files.length > maxFiles) {
      feedback.textContent = isPremium ?
        `You can only upload a maximum of ${maxFiles} images.` :
        `Non-premium users can only upload 1 image.`;
      e.target.classList.add('is-invalid');
      // Clear the file input
      e.target.value = '';
    } else {
      e.target.classList.remove('is-invalid');
    }
  });


</script>
{% endblock %}