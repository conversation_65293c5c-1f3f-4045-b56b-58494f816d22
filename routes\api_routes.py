"""
API Routes Module

This module handles all API endpoints for the application.
"""

from flask import Blueprint, jsonify, request, g, session
from utils.security import login_required, content_manager_required
from services import location_service, message_service, user_service, notification_service, journey_service, event_service
from utils.permissions import Per<PERSON><PERSON>hecker
from utils.logger import get_logger

logger = get_logger(__name__)

bp = Blueprint('api', __name__, url_prefix='/api')

# User API endpoints
@bp.route('/users/search', methods=['GET'])
@login_required
def search_users():
    """Search for users by username"""
    query = request.args.get('q', '')
    if not query or len(query) < 2:
        return jsonify([])

    users = user_service.search_users(query)
    users = [u for u in users if u.get('id') != session['user_id']]
    return jsonify(users)

@bp.route('/users/<int:user_id>', methods=['GET'])
@login_required
def get_user(user_id):
    """Get user details by ID"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    # Online status not available in non-real-time mode
    user['online'] = False

    return jsonify(user)

# Message API endpoints
@bp.route('/messages/conversations', methods=['GET'])
@login_required
def get_conversations():
    """Get all conversations for the current user"""
    limit = request.args.get('limit', type=int)
    conversations = message_service.get_conversations(session['user_id'])

    # Get user details for each conversation
    for conversation in conversations:
        other_user = user_service.get_user_by_id(conversation['other_user_id'])
        if other_user:
            conversation['other_user'] = other_user

            # Online status not available in non-real-time mode
            conversation['online'] = False

    # Apply limit if specified (for navbar dropdown)
    if limit:
        conversations = conversations[:limit]

    return jsonify(conversations)

@bp.route('/messages/<int:other_user_id>', methods=['GET'])
@login_required
def get_messages(other_user_id):
    """Get messages between current user and another user"""
    messages = message_service.get_messages_between(session['user_id'], other_user_id)
    return jsonify(messages)

@bp.route('/messages/unread-count', methods=['GET'])
@login_required
def get_unread_count():
    """Get count of unread messages for current user"""
    count = message_service.get_unread_messages_count(session['user_id'])
    return jsonify({'count': count})

@bp.route('/notifications/unread-count', methods=['GET'])
@login_required
def get_notification_unread_count():
    """Get count of unread notifications for current user"""
    count = notification_service.count_unread_notifications(session['user_id'])
    return jsonify({'count': count})

# Placeholder API for testing
@bp.route('/placeholder/<int:width>/<int:height>', methods=['GET'])
def placeholder_image(width, height):
    """Return a placeholder image URL"""
    return f"https://via.placeholder.com/{width}x{height}"


@bp.route('/location-coords')
def get_location_coords():
    location_name = request.args.get('name')
    if not location_name:
        return jsonify({'error': 'Location name is required'}), 400

    location = location_service.get_location_by_name(location_name)
    if not location:
        return jsonify({'error': 'Location not found'}), 404

    return jsonify({'lat': location['latitude'], 'lng': location['longitude']})


# Journey Operations API
@bp.route('/journey/<int:journey_id>/toggle-follow', methods=['POST'])
@login_required
def toggle_journey_follow(journey_id):
    """Toggle follow status for a journey"""
    try:
        from services import departure_board_service

        # Check if currently following
        is_following = departure_board_service.is_following_journey(
            user_id=session['user_id'],
            journey_id=journey_id
        )

        if is_following:
            success, message = departure_board_service.unfollow_journey(
                user_id=session['user_id'],
                journey_id=journey_id
            )
            action = 'unfollowed'
        else:
            success, message = departure_board_service.follow_journey(
                user_id=session['user_id'],
                journey_id=journey_id
            )
            action = 'followed'

        return jsonify({
            'success': success,
            'message': message,
            'action': action,
            'is_following': not is_following if success else is_following
        })

    except Exception as e:
        logger.error(f"Error toggling journey follow: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while updating follow status'
        }), 500

@bp.route('/journey/<int:journey_id>/visibility', methods=['POST'])
@login_required
def update_journey_visibility(journey_id):
    """Update journey visibility"""
    try:
        data = request.get_json()
        new_visibility = data.get('visibility')
        edit_reason = data.get('edit_reason')

        if not new_visibility:
            return jsonify({
                'success': False,
                'message': 'Visibility is required'
            }), 400

        success, message = journey_service.update_journey(
            journey_id=journey_id,
            user_id=session['user_id'],
            visibility=new_visibility,
            edit_reason=edit_reason
        )

        return jsonify({
            'success': success,
            'message': message,
            'new_visibility': new_visibility if success else None
        })

    except Exception as e:
        logger.error(f"Error updating journey visibility: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while updating visibility'
        }), 500

@bp.route('/journey/<int:journey_id>/hidden-status', methods=['POST'])
@login_required
@content_manager_required
def toggle_journey_hidden_status(journey_id):
    """Toggle journey hidden status (staff only)"""
    try:
        success, message, journey = journey_service.get_journey(
            journey_id=journey_id,
            user_id=session['user_id']
        )

        if not success:
            return jsonify({
                'success': False,
                'message': message
            }), 404

        new_hidden_status = not journey.get('is_hidden', False)

        success, message = journey_service.update_journey_hidden_status(
            journey_id=journey_id,
            user_id=session['user_id'],
            is_hidden=new_hidden_status
        )

        return jsonify({
            'success': success,
            'message': message,
            'is_hidden': new_hidden_status if success else journey.get('is_hidden', False)
        })

    except Exception as e:
        logger.error(f"Error toggling journey hidden status: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while updating hidden status'
        }), 500

# Event Operations API
@bp.route('/event/<int:event_id>/like', methods=['POST'])
@login_required
def toggle_event_like(event_id):
    """Toggle like status for an event"""
    try:
        success, message, current_likes = event_service.toggle_event_like(
            event_id=event_id,
            user_id=session['user_id']
        )

        return jsonify({
            'success': success,
            'message': message,
            'likes_count': current_likes,
            'user_liked': success  # If successful, it means the like was toggled
        })

    except Exception as e:
        logger.error(f"Error toggling event like: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while updating like status'
        }), 500

# Location Operations API
@bp.route('/location/search', methods=['GET'])
def search_locations():
    """Search for existing locations"""
    try:
        query = request.args.get('query', '').strip()

        if len(query) < 2:
            return jsonify([])

        success, message, locations = location_service.search_locations(query)

        if not success:
            return jsonify({
                'success': False,
                'message': message
            }), 500

        return jsonify(locations)

    except Exception as e:
        logger.error(f"Error searching locations: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while searching locations'
        }), 500
