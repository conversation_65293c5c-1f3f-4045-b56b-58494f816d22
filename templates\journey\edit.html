{% block head %}
<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/journey-operations.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid" id="editJourneyModal">
  <div class="row justify-content-center">
    <form method="post" id="editJourneyForm" action="{{ url_for('journey.update_journey', journey_id=journey.id) }}"
      enctype="multipart/form-data" class="needs-validation" novalidate>

      <div class="row">
        <!-- Form Inputs -->
        <div class="col-12">
          <div class="mb-3">
            <label for="title" class="form-label">Title *</label>
            <input type="text" class="form-control" id="title" name="title" value="{{ journey.title }}" required
              minlength="5" maxlength="50" />
            <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
          </div>

          {% if journey.user_id == session.user_id %}
          <div class="mb-3">
            <label for="start_date" class="form-label">Start Date *</label>
            <input type="date" class="form-control" id="start_date" name="start_date"
              value="{{ journey.start_date.strftime('%Y-%m-%d') }}" required />
            <div class="invalid-feedback">Start date is required.</div>
          </div>
          {% else %}
          <div class="mb-3">
            <label class="form-label">Start Date</label>
            <p class="form-control-plaintext">{{ journey.start_date.strftime('%d-%m-%Y') }}</p>
            <input type="hidden" id="start_date" name="start_date" value="{{ journey.start_date.strftime('%Y-%m-%d') }}"
              readonly />
          </div>
          {% endif %}

          <div class="mb-3">
            <label for="description" class="form-label">Description *</label>
            <textarea class="form-control" id="description" name="description" rows="4" required minlength="5"
              maxlength="250">{{ journey.description }}</textarea>
            <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
          </div>

          <div class="mb-3">
            {% if journey.user_id == session.user_id and not user_blocked%}
            <label for="visibility" class="form-label">Visibility</label>
            <select id="visibility" name="visibility" class="form-select">
              <option value="public" {% if journey.visibility=='public' %}selected{% endif %}>Public</option>
              <option value="private" {% if journey.visibility=='private' %}selected{% endif %}>Private</option>
              {% if premium_access or journey.visibility=='published' %} <option value="published" {% if
                journey.visibility=='published' %}selected{% endif %}>Published</option>{% endif %}
            </select>
            {% elif user_blocked %}
            <div class="alert rounded-4"
              style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2);">
              <div class="d-flex align-items-center">
                <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                <p class="mb-0">You have been blocked from sharing journeys publicly.</p>
              </div>
            </div>
            {% else %}
            <div>
              <span
                class="badge rounded-pill {% if journey.visibility in ('public', 'published') %}text-bg-success{% else %}text-bg-secondary{% endif %} px-3 py-2">
                {{ journey.visibility|capitalize }}
              </span>
              <input type="hidden" name="visibility" value="{{ journey.visibility }}" />
            </div>
          </div>
          {% endif %}
        </div>

        {% if journey.user_id == session.user_id and (premium_access or (ever_had_premium and journey.no_edits)) %}
        <div class="mb-3 form-check">
          <input type="checkbox" class="form-check-input" id="no_edits" name="no_edits"
                 {% if journey.no_edits %}checked{% endif %}
                 {% if not premium_access and journey.no_edits %}disabled{% endif %}>
          <label class="form-check-label" for="no_edits">Prevent editors from editing this journey</label>
          <div class="form-text">
            {% if premium_access %}
              When checked, editors and admins cannot edit your journey content, but they can still hide it if needed.
            {% elif ever_had_premium and journey.no_edits %}
              This journey is protected from edits. To change this setting, you need an active premium subscription.
            {% endif %}
          </div>
        </div>
        {% endif %}

        {% if journey.user_id != session.user_id and session.get('role') in ['editor', 'admin', 'support_tech'] %}
        <div class="mb-3">
          <label for="edit_reason" class="form-label">Edit Reason</label>
          <textarea class="form-control" id="edit_reason" name="edit_reason" rows="2" required
            placeholder="Please provide a reason for this edit"></textarea>
          <div class="invalid-feedback">Staff must provide a reason for editing user content</div>
          <div class="form-text">As a staff member, you must provide a reason when editing user content.</div>
        </div>
        {% endif %}
      </div>


  </div>
  </form>
</div>
</div>


{% endblock %}