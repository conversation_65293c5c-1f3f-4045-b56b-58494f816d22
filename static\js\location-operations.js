/**
 * Location Operations Utility
 * 
 * Provides centralized functions for location search, map handling, and coordinate management
 * Integrates with backend location services
 * 
 * Dependencies: Leaflet.js, flash-messages.js
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class LocationOperations {
    constructor() {
        this.baseUrl = '/api/location';
        this.map = null;
        this.marker = null;
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.bindEventListeners();
        this.setupMapIcons();
    }

    /**
     * Setup map icons
     */
    setupMapIcons() {
        if (typeof L !== 'undefined') {
            this.redIcon = L.icon({
                iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
                shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34],
                shadowSize: [41, 41]
            });
        }
    }

    /**
     * Bind event listeners for location operations
     */
    bindEventListeners() {
        // Location search inputs
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-location-search]')) {
                this.handleLocationSearch(e.target);
            }
        });

        // Map search inputs
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-map-search]')) {
                this.handleMapSearch(e.target);
            }
        });

        // Location selection
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="select-location"]')) {
                e.preventDefault();
                const locationData = JSON.parse(e.target.dataset.locationData);
                this.selectLocation(locationData);
            }
        });

        // Hide dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.location-search-container')) {
                this.hideAllDropdowns();
            }
        });
    }

    /**
     * Handle location search (database search)
     */
    async handleLocationSearch(input) {
        clearTimeout(this.searchTimeout);
        
        const query = input.value.trim();
        const dropdown = this.getDropdownForInput(input);
        
        if (query.length < 2) {
            this.hideDropdown(dropdown);
            return;
        }

        this.searchTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`${this.baseUrl}/search?query=${encodeURIComponent(query)}`);
                const locations = await response.json();
                
                this.displayLocationResults(dropdown, locations, 'database');
            } catch (error) {
                console.error('Error searching locations:', error);
                this.hideDropdown(dropdown);
            }
        }, 300);
    }

    /**
     * Handle map search (external geocoding)
     */
    async handleMapSearch(input) {
        clearTimeout(this.searchTimeout);
        
        const query = input.value.trim();
        const dropdown = this.getDropdownForInput(input);
        
        if (query.length < 3) {
            this.hideDropdown(dropdown);
            return;
        }

        this.searchTimeout = setTimeout(async () => {
            try {
                const response = await fetch(
                    `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1`
                );
                const locations = await response.json();
                
                this.displayLocationResults(dropdown, locations, 'map');
            } catch (error) {
                console.error('Error searching map locations:', error);
                this.hideDropdown(dropdown);
            }
        }, 500);
    }

    /**
     * Display location search results
     */
    displayLocationResults(dropdown, locations, type) {
        if (!dropdown) return;

        dropdown.innerHTML = '';

        if (locations.length === 0) {
            dropdown.innerHTML = '<div class="no-results">No locations found</div>';
            this.showDropdown(dropdown);
            return;
        }

        locations.forEach(location => {
            const item = this.createLocationItem(location, type);
            dropdown.appendChild(item);
        });

        this.showDropdown(dropdown);
    }

    /**
     * Create location result item
     */
    createLocationItem(location, type) {
        const item = document.createElement('div');
        item.className = 'location-result-item';
        
        let displayName, lat, lng, locationData;
        
        if (type === 'database') {
            displayName = location.name;
            lat = location.latitude;
            lng = location.longitude;
            locationData = {
                name: location.name,
                latitude: lat,
                longitude: lng,
                type: 'existing'
            };
        } else {
            displayName = location.display_name;
            lat = parseFloat(location.lat);
            lng = parseFloat(location.lon);
            locationData = {
                name: displayName,
                latitude: lat,
                longitude: lng,
                type: 'new'
            };
        }

        item.innerHTML = `
            <div class="location-info">
                <div class="location-icon">
                    <i class="bi bi-geo-alt"></i>
                </div>
                <div class="location-details">
                    <div class="location-name">${displayName}</div>
                    <div class="location-type">${type === 'database' ? 'Existing Location' : 'New Location'}</div>
                </div>
            </div>
        `;

        item.dataset.action = 'select-location';
        item.dataset.locationData = JSON.stringify(locationData);

        return item;
    }

    /**
     * Select a location
     */
    async selectLocation(locationData) {
        try {
            // Update location input
            const locationInput = document.getElementById('location');
            if (locationInput) {
                locationInput.value = locationData.name;
            }

            // Update coordinates
            this.updateCoordinates(locationData.latitude, locationData.longitude);

            // Update map if available
            if (this.map && this.marker) {
                this.map.setView([locationData.latitude, locationData.longitude], 15);
                this.marker.setLatLng([locationData.latitude, locationData.longitude]);
            }

            // Hide dropdowns
            this.hideAllDropdowns();

            // Show location section
            const locationSection = document.getElementById('selectedLocationSection');
            if (locationSection) {
                locationSection.style.display = 'block';
            }

            // Show map section
            const mapSection = document.getElementById('mapSection');
            if (mapSection) {
                mapSection.style.display = 'block';
            }

            // Initialize map if not already done
            if (!this.map) {
                setTimeout(() => {
                    this.initializeMap(locationData.latitude, locationData.longitude);
                }, 100);
            }

        } catch (error) {
            console.error('Error selecting location:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Error selecting location. Please try again.', 'danger');
            }
        }
    }

    /**
     * Initialize map
     */
    initializeMap(lat, lng) {
        const mapElement = document.getElementById('map');
        if (!mapElement || typeof L === 'undefined') return;

        try {
            // Remove existing map if any
            if (this.map) {
                this.map.remove();
            }

            // Create new map
            this.map = L.map('map').setView([lat, lng], 15);
            
            L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
            }).addTo(this.map);

            // Add marker
            this.marker = L.marker([lat, lng], { 
                icon: this.redIcon,
                draggable: true 
            }).addTo(this.map);

            // Handle marker drag
            this.marker.on('dragend', (e) => {
                const position = e.target.getLatLng();
                this.updateCoordinates(position.lat, position.lng);
                this.reverseGeocode(position.lat, position.lng);
            });

            // Handle map click
            this.map.on('click', (e) => {
                this.marker.setLatLng(e.latlng);
                this.updateCoordinates(e.latlng.lat, e.latlng.lng);
                this.reverseGeocode(e.latlng.lat, e.latlng.lng);
            });

        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    /**
     * Update coordinate inputs
     */
    updateCoordinates(lat, lng) {
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');

        if (latInput) latInput.value = lat;
        if (lngInput) lngInput.value = lng;

        // Show coordinates status
        const statusElement = document.getElementById('coordinatesStatus');
        if (statusElement) {
            statusElement.style.display = 'block';
            const textElement = document.getElementById('coordinatesText');
            if (textElement) {
                textElement.textContent = `Location coordinates set (${lat.toFixed(6)}, ${lng.toFixed(6)})`;
            }
        }
    }

    /**
     * Reverse geocode coordinates to address
     */
    async reverseGeocode(lat, lng) {
        try {
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
            );
            const data = await response.json();

            const mapInput = document.getElementById('mapSearch');
            if (mapInput) {
                mapInput.value = data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            }
        } catch (error) {
            console.error('Error reverse geocoding:', error);
        }
    }

    /**
     * Get dropdown element for input
     */
    getDropdownForInput(input) {
        const container = input.closest('.location-search-container') || 
                         input.closest('.form-group');
        return container ? container.querySelector('.location-dropdown, .results-list, #locationSuggestions, #mapSuggestions') : null;
    }

    /**
     * Show dropdown
     */
    showDropdown(dropdown) {
        if (dropdown) {
            dropdown.classList.remove('d-none');
            dropdown.classList.add('show');
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown(dropdown) {
        if (dropdown) {
            dropdown.classList.add('d-none');
            dropdown.classList.remove('show');
        }
    }

    /**
     * Hide all dropdowns
     */
    hideAllDropdowns() {
        const dropdowns = document.querySelectorAll('.location-dropdown, .results-list, #locationSuggestions, #mapSuggestions');
        dropdowns.forEach(dropdown => this.hideDropdown(dropdown));
    }

    /**
     * Get coordinates for location name
     */
    async getLocationCoordinates(locationName) {
        try {
            const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
            const data = await response.json();
            
            if (data.lat && data.lng) {
                return {
                    latitude: parseFloat(data.lat),
                    longitude: parseFloat(data.lng)
                };
            }
            
            return null;
        } catch (error) {
            console.error('Error getting location coordinates:', error);
            return null;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.locationOperations = new LocationOperations();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocationOperations;
}
