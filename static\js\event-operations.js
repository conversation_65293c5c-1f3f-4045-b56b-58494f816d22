/**
 * Event Operations Utility
 * 
 * Provides centralized functions for event-related operations
 * Moves business logic to backend API calls
 * 
 * Dependencies: flash-messages.js, modal.html
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class EventOperations {
    constructor() {
        this.baseUrl = '/api/event';
        this.init();
    }

    init() {
        this.bindEventListeners();
    }

    /**
     * Bind event listeners for event operations
     */
    bindEventListeners() {
        // Like buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="toggle-like"]') || 
                e.target.closest('[data-action="toggle-like"]')) {
                e.preventDefault();
                const button = e.target.matches('[data-action="toggle-like"]') ? 
                              e.target : e.target.closest('[data-action="toggle-like"]');
                const eventId = button.dataset.eventId;
                this.toggleLike(eventId, button);
            }
        });

        // Edit event buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="edit-event"]')) {
                e.preventDefault();
                const eventId = e.target.dataset.eventId;
                this.openEditModal(eventId);
            }
        });

        // Delete event buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="delete-event"]')) {
                e.preventDefault();
                const eventId = e.target.dataset.eventId;
                this.confirmDelete(eventId);
            }
        });

        // Create event buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="create-event"]')) {
                e.preventDefault();
                const journeyId = e.target.dataset.journeyId;
                this.openCreateModal(journeyId);
            }
        });
    }

    /**
     * Toggle like status for an event
     */
    async toggleLike(eventId, button) {
        try {
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/${eventId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.success) {
                // Update like button
                this.updateLikeButton(button, data.user_liked, data.likes_count);
                
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'success');
                }
            } else {
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'danger');
                }
            }
        } catch (error) {
            console.error('Error toggling like:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        } finally {
            button.disabled = false;
        }
    }

    /**
     * Open edit event modal
     */
    async openEditModal(eventId) {
        try {
            const response = await fetch(`/event/${eventId}/edit`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const formHtml = await response.text();
            
            if (typeof showModal === 'function') {
                showModal('Edit Event', formHtml, {
                    actionText: 'Save',
                    onAction: async () => {
                        return await this.submitEditForm();
                    }
                });
                
                // Initialize form components after modal is shown
                setTimeout(() => {
                    this.initializeEditForm();
                }, 200);
            } else {
                console.error('showModal function not available');
            }
        } catch (error) {
            console.error('Error opening edit modal:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Failed to load edit form. Please try again.', 'danger');
            }
        }
    }

    /**
     * Open create event modal
     */
    async openCreateModal(journeyId) {
        try {
            const response = await fetch(`/event/new/${journeyId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const formHtml = await response.text();
            
            if (typeof showModal === 'function') {
                showModal('Add New Event', formHtml, {
                    actionText: 'Create',
                    onAction: async () => {
                        return await this.submitCreateForm();
                    }
                });
                
                // Initialize form components after modal is shown
                setTimeout(() => {
                    this.initializeCreateForm();
                }, 200);
            } else {
                console.error('showModal function not available');
            }
        } catch (error) {
            console.error('Error opening create modal:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Failed to load create form. Please try again.', 'danger');
            }
        }
    }

    /**
     * Submit edit form
     */
    async submitEditForm() {
        const form = document.getElementById('editEventForm');
        if (!form) return false;

        // Validate form
        form.classList.add('was-validated');
        
        if (!form.checkValidity()) {
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return false;
        }

        try {
            const formData = new FormData(form);
            
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            if (response.redirected) {
                window.location.href = response.url;
            } else {
                window.location.reload();
            }
            
            return true;
        } catch (error) {
            console.error('Error submitting edit form:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Failed to save changes. Please try again.', 'danger');
            }
            return false;
        }
    }

    /**
     * Submit create form
     */
    async submitCreateForm() {
        const form = document.getElementById('createEventForm');
        if (!form) return false;

        // Validate form
        form.classList.add('was-validated');
        
        if (!form.checkValidity()) {
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return false;
        }

        // Check location selection
        const locationInput = document.getElementById('location');
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');

        if (!locationInput?.value || !latInput?.value || !lngInput?.value) {
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Please select a location first.', 'warning');
            }
            return false;
        }

        try {
            form.submit();
            return true;
        } catch (error) {
            console.error('Error submitting create form:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Failed to create event. Please try again.', 'danger');
            }
            return false;
        }
    }

    /**
     * Initialize edit form components
     */
    initializeEditForm() {
        // Initialize map if needed
        if (typeof initMap === 'function') {
            initMap();
        }
        
        // Initialize location search if needed
        if (typeof initializeModalLocationSearch === 'function') {
            initializeModalLocationSearch();
        }
    }

    /**
     * Initialize create form components
     */
    initializeCreateForm() {
        // Initialize map if needed
        if (typeof initMap === 'function') {
            initMap();
        }
        
        // Initialize location search if needed
        if (typeof window.initializeLocationSelector === 'function') {
            window.initializeLocationSelector();
        }
    }

    /**
     * Confirm and delete event
     */
    confirmDelete(eventId) {
        if (typeof showModal === 'function') {
            showModal(
                'Delete Event',
                'Are you sure you want to delete this event? This action cannot be undone.',
                {
                    actionText: 'Delete',
                    onAction: () => {
                        this.deleteEvent(eventId);
                    }
                }
            );
        } else {
            // Fallback to browser confirm
            if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
                this.deleteEvent(eventId);
            }
        }
    }

    /**
     * Delete event
     */
    async deleteEvent(eventId) {
        try {
            const deleteForm = document.getElementById(`deleteEventForm_${eventId}`);
            
            if (deleteForm) {
                deleteForm.submit();
            } else {
                console.error('Delete form not found');
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage('Delete form not found. Please refresh and try again.', 'danger');
                }
            }
        } catch (error) {
            console.error('Error deleting event:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        }
    }

    /**
     * Update like button appearance
     */
    updateLikeButton(button, isLiked, likesCount) {
        const icon = button.querySelector('i');
        const countSpan = button.querySelector('span');
        
        if (icon) {
            icon.className = isLiked ? 'bi bi-heart-fill' : 'bi bi-heart';
        }
        
        if (countSpan) {
            countSpan.textContent = likesCount || 0;
        }
        
        // Update button styling
        if (isLiked) {
            button.classList.add('liked');
        } else {
            button.classList.remove('liked');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.eventOperations = new EventOperations();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EventOperations;
}
